import { supabase } from '@/integrations/supabase/client';

export interface ProductReview {
  id: string;
  product_id: string;
  customer_id: string;
  customer_name: string;
  rating: number;
  title?: string;
  comment: string;
  is_verified: boolean;
  helpful_count: number;
  created_at: string;
  updated_at: string;
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface CreateReviewData {
  product_id: string;
  customer_id: string;
  customer_name: string;
  rating: number;
  title?: string;
  comment: string;
}

class ReviewService {
  // Get reviews for a product
  async getProductReviews(productId: string, limit: number = 10, offset: number = 0): Promise<ProductReview[]> {
    try {
      const { data, error } = await supabase
        .from('product_reviews')
        .select('*')
        .eq('product_id', productId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching product reviews:', error);
        throw error;
      }

      // Get customer names separately to avoid join issues
      const customerIds = [...new Set((data || []).map(review => review.customer_id).filter(Boolean))];
      let customerNames: { [key: string]: string } = {};

      if (customerIds.length > 0) {
        const { data: users } = await supabase
          .from('users')
          .select('id, full_name')
          .in('id', customerIds);

        customerNames = (users || []).reduce((acc, user) => {
          acc[user.id] = user.full_name;
          return acc;
        }, {} as { [key: string]: string });
      }

      return (data || []).map(review => ({
        id: review.id,
        product_id: review.product_id,
        customer_id: review.customer_id,
        customer_name: customerNames[review.customer_id] || 'Anonymous',
        rating: review.rating,
        title: review.title || '',
        comment: review.comment || '',
        is_verified: review.is_verified || false,
        helpful_count: review.helpful_count || 0,
        created_at: review.created_at,
        updated_at: review.updated_at
      }));
    } catch (error) {
      console.error('Error in getProductReviews:', error);
      return [];
    }
  }

  // Get review statistics for a product
  async getReviewStats(productId: string): Promise<ReviewStats> {
    try {
      const { data, error } = await supabase
        .from('product_reviews')
        .select('rating')
        .eq('product_id', productId);

      if (error) {
        console.error('Error fetching review stats:', error);
        throw error;
      }

      const reviews = data || [];
      const totalReviews = reviews.length;

      if (totalReviews === 0) {
        return {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
        };
      }

      const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
      
      const ratingDistribution = reviews.reduce((dist, review) => {
        dist[review.rating as keyof typeof dist]++;
        return dist;
      }, { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 });

      return {
        averageRating,
        totalReviews,
        ratingDistribution
      };
    } catch (error) {
      console.error('Error in getReviewStats:', error);
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
      };
    }
  }

  // Create a new review
  async createReview(reviewData: CreateReviewData): Promise<ProductReview> {
    try {
      // Check if user already reviewed this product
      const { data: existingReview } = await supabase
        .from('product_reviews')
        .select('id')
        .eq('product_id', reviewData.product_id)
        .eq('customer_id', reviewData.customer_id)
        .single();

      if (existingReview) {
        throw new Error('You have already reviewed this product');
      }

      const { data, error } = await supabase
        .from('product_reviews')
        .insert({
          product_id: reviewData.product_id,
          customer_id: reviewData.customer_id,
          rating: reviewData.rating,
          title: reviewData.title,
          comment: reviewData.comment,
          is_verified: false // Would be set based on purchase history
        })
        .select(`
          *,
          users!product_reviews_customer_id_fkey (
            full_name
          )
        `)
        .single();

      if (error) {
        console.error('Error creating review:', error);
        throw error;
      }

      return {
        id: data.id,
        product_id: data.product_id,
        customer_id: data.customer_id,
        customer_name: data.users?.full_name || reviewData.customer_name,
        rating: data.rating,
        title: data.title,
        comment: data.comment,
        is_verified: data.is_verified,
        helpful_count: data.helpful_count,
        created_at: data.created_at,
        updated_at: data.updated_at
      };
    } catch (error) {
      console.error('Error in createReview:', error);
      throw error;
    }
  }

  // Update a review
  async updateReview(reviewId: string, updates: Partial<CreateReviewData>): Promise<ProductReview> {
    try {
      const { data, error } = await supabase
        .from('product_reviews')
        .update({
          rating: updates.rating,
          title: updates.title,
          comment: updates.comment,
          updated_at: new Date().toISOString()
        })
        .eq('id', reviewId)
        .select(`
          *,
          users!product_reviews_customer_id_fkey (
            full_name
          )
        `)
        .single();

      if (error) {
        console.error('Error updating review:', error);
        throw error;
      }

      return {
        id: data.id,
        product_id: data.product_id,
        customer_id: data.customer_id,
        customer_name: data.users?.full_name || 'Anonymous',
        rating: data.rating,
        title: data.title,
        comment: data.comment,
        is_verified: data.is_verified,
        helpful_count: data.helpful_count,
        created_at: data.created_at,
        updated_at: data.updated_at
      };
    } catch (error) {
      console.error('Error in updateReview:', error);
      throw error;
    }
  }

  // Delete a review
  async deleteReview(reviewId: string, customerId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('product_reviews')
        .delete()
        .eq('id', reviewId)
        .eq('customer_id', customerId);

      if (error) {
        console.error('Error deleting review:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in deleteReview:', error);
      throw error;
    }
  }

  // Mark review as helpful
  async markReviewHelpful(reviewId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('product_reviews')
        .update({
          helpful_count: supabase.sql`helpful_count + 1`
        })
        .eq('id', reviewId);

      if (error) {
        console.error('Error marking review as helpful:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error in markReviewHelpful:', error);
      throw error;
    }
  }

  // Check if user has reviewed a product
  async hasUserReviewed(productId: string, customerId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('product_reviews')
        .select('id')
        .eq('product_id', productId)
        .eq('customer_id', customerId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error('Error checking if user reviewed:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error in hasUserReviewed:', error);
      return false;
    }
  }

  // Subscribe to review changes for a product
  subscribeToProductReviews(productId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`product-reviews-${productId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'product_reviews',
          filter: `product_id=eq.${productId}`
        }, 
        callback
      )
      .subscribe();
  }
}

export const reviewService = new ReviewService();
export default reviewService;
