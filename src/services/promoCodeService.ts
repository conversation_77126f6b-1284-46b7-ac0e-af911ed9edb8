
import { supabase } from '../integrations/supabase/client';
import { realTimeService } from './realTimeService';

export interface PromoCode {
  id: string;
  code: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  minimum_order?: number;
  usage_limit?: number;
  used_count: number;
  expires_at?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PromoCodeValidation {
  isValid: boolean;
  discount: number;
  discountType: 'percentage' | 'fixed';
  message: string;
  promoCode?: PromoCode;
}

class PromoCodeService {
  private promoCodes: PromoCode[] = [];
  private subscribers: ((promoCodes: PromoCode[]) => void)[] = [];

  constructor() {
    this.initializeRealTimeSubscription();
  }

  private initializeRealTimeSubscription() {
    // Subscribe to real-time updates from admin dashboard
    realTimeService.subscribe('promo-code-created', (data) => {
      console.log('PromoCodeService: New promo code created', data);
      this.loadActivePromoCodes();
    });

    realTimeService.subscribe('promo-code-updated', (data) => {
      console.log('PromoCodeService: Promo code updated', data);
      this.loadActivePromoCodes();
    });

    // Subscribe to database changes
    supabase
      .channel('promo-codes-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'promo_codes' },
        (payload) => {
          console.log('PromoCodeService: Database change detected', payload);
          this.loadActivePromoCodes();
        }
      )
      .subscribe();
  }

  async loadActivePromoCodes(): Promise<PromoCode[]> {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading active promo codes:', error);
        return this.promoCodes;
      }

      // Filter out expired codes
      const now = new Date();
      const activePromoCodes = (data || []).filter(promo => {
        if (!promo.expires_at) return true;
        return new Date(promo.expires_at) > now;
      });

      this.promoCodes = activePromoCodes;
      this.notifySubscribers();

      console.log('Loaded active promo codes:', activePromoCodes);
      return activePromoCodes;
    } catch (error) {
      console.error('Error loading active promo codes:', error);
      return this.promoCodes;
    }
  }

  async getActivePromoCodes(): Promise<PromoCode[]> {
    if (this.promoCodes.length === 0) {
      await this.loadActivePromoCodes();
    }
    return this.promoCodes;
  }

  async validatePromoCode(code: string, orderTotal: number): Promise<PromoCodeValidation> {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return {
          isValid: false,
          discount: 0,
          discountType: 'percentage',
          message: 'Invalid promo code'
        };
      }

      const promoCode = data as PromoCode;

      // Check if expired
      if (promoCode.expires_at && new Date(promoCode.expires_at) <= new Date()) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: 'This promo code has expired'
        };
      }

      // Check minimum order requirement
      if (promoCode.minimum_order && orderTotal < promoCode.minimum_order) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: `Minimum order amount is ${promoCode.minimum_order} Dh`
        };
      }

      // Check usage limit
      if (promoCode.usage_limit && promoCode.used_count >= promoCode.usage_limit) {
        return {
          isValid: false,
          discount: 0,
          discountType: promoCode.discount_type,
          message: 'This promo code has reached its usage limit'
        };
      }

      // Calculate discount
      let discount = 0;
      if (promoCode.discount_type === 'percentage') {
        discount = (orderTotal * promoCode.discount_value) / 100;
      } else {
        discount = promoCode.discount_value;
      }

      return {
        isValid: true,
        discount: Math.min(discount, orderTotal), // Don't exceed order total
        discountType: promoCode.discount_type,
        message: `Promo code applied! You saved ${discount.toFixed(2)} Dh`,
        promoCode
      };
    } catch (error) {
      console.error('Error validating promo code:', error);
      return {
        isValid: false,
        discount: 0,
        discountType: 'percentage',
        message: 'Error validating promo code'
      };
    }
  }

  async applyPromoCode(code: string): Promise<void> {
    try {
      // Increment usage count
      const { error } = await supabase
        .from('promo_codes')
        .update({
          used_count: supabase.sql`used_count + 1`,
          updated_at: new Date().toISOString()
        })
        .eq('code', code.toUpperCase());

      if (error) {
        console.error('Error applying promo code:', error);
      } else {
        console.log('Promo code usage count incremented:', code);
      }
    } catch (error) {
      console.error('Error applying promo code:', error);
    }
  }

  subscribe(callback: (promoCodes: PromoCode[]) => void): () => void {
    this.subscribers.push(callback);

    // Immediately call with current data
    callback(this.promoCodes);

    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.promoCodes));
  }

  // Get promo codes for display in shopping cart
  async getAvailableOffers(): Promise<PromoCode[]> {
    const activePromoCodes = await this.getActivePromoCodes();

    // Return only codes that haven't reached their usage limit
    return activePromoCodes.filter(promo => {
      if (!promo.usage_limit) return true;
      return promo.used_count < promo.usage_limit;
    });
  }
}

// Export singleton instance
export const promoCodeService = new PromoCodeService();

// Legacy exports for backward compatibility
export const getPromoCodes = () => promoCodeService.getActivePromoCodes();
export const getActivePromoCodes = () => promoCodeService.getActivePromoCodes();

export const validatePromoCode = (code: string, orderAmount: number) => promoCodeService.validatePromoCode(code, orderAmount);
export const applyPromoCode = (code: string) => promoCodeService.applyPromoCode(code);

// Additional exports for admin functionality
export const createPromoCode = async (promoData: Omit<PromoCode, 'id' | 'created_at' | 'updated_at' | 'used_count'>): Promise<PromoCode> => {
  try {
    const { data, error } = await supabase
      .from('promo_codes')
      .insert({
        code: promoData.code,
        discount_type: promoData.discount_type,
        discount_value: promoData.discount_value,
        minimum_order: promoData.minimum_order || null,
        usage_limit: promoData.usage_limit || null,
        expires_at: promoData.expires_at || null,
        is_active: promoData.is_active,
        used_count: 0
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error creating promo code:', error);
      throw error;
    }

    const newPromo = data as PromoCode;

    // Emit real-time event
    realTimeService.emit('promo-code-created', {
      promoCode: newPromo,
      timestamp: new Date().toISOString()
    });

    return newPromo;
  } catch (error) {
    console.error('Error creating promo code:', error);
    throw error;
  }
};

export const updatePromoCode = async (id: string, updates: Partial<PromoCode>): Promise<PromoCode | null> => {
  try {
    const { data, error } = await supabase
      .from('promo_codes')
      .update({
        code: updates.code,
        discount_type: updates.discount_type,
        discount_value: updates.discount_value,
        minimum_order: updates.minimum_order || null,
        usage_limit: updates.usage_limit || null,
        expires_at: updates.expires_at || null,
        is_active: updates.is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating promo code:', error);
      throw error;
    }

    const updatedPromo = data as PromoCode;

    // Emit real-time event
    realTimeService.emit('promo-code-updated', {
      promoCode: updatedPromo,
      timestamp: new Date().toISOString()
    });

    return updatedPromo;
  } catch (error) {
    console.error('Error updating promo code:', error);
    throw error;
  }
};

export const deletePromoCode = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('promo_codes')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting promo code:', error);
      return false;
    }

    // Emit real-time event
    realTimeService.emit('promo-code-deleted', {
      promoCodeId: id,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('Error deleting promo code:', error);
    return false;
  }
};

export default promoCodeService;
