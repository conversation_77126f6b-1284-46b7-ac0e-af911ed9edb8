import { Order, OrderStatus, PaymentStatus } from '../types/order';
import { generateOrderId, calculateEstimatedDelivery } from '../utils/orderUtils';
import { addOrderTracking } from './orderTrackingService';
import { getProductById, updateStock } from './inventoryService';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';

// Type aliases for better readability
type OrderRow = Database['public']['Tables']['orders']['Row'];
type OrderInsert = Database['public']['Tables']['orders']['Insert'];
type OrderUpdate = Database['public']['Tables']['orders']['Update'];
type OrderItemRow = Database['public']['Tables']['order_items']['Row'];
type OrderItemInsert = Database['public']['Tables']['order_items']['Insert'];
type OrderTrackingRow = Database['public']['Tables']['order_tracking']['Row'];
type OrderTrackingInsert = Database['public']['Tables']['order_tracking']['Insert'];
type PaymentRow = Database['public']['Tables']['payments']['Row'];
type PaymentInsert = Database['public']['Tables']['payments']['Insert'];

// Event system for real-time order updates
type OrderEventType = 'order-created' | 'order-updated' | 'order-status-changed' | 'payment-updated';
type OrderEventListener = (eventType: OrderEventType, data: any) => void;

class OrderEventManager {
  private listeners: OrderEventListener[] = [];

  subscribe(listener: OrderEventListener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  emit(eventType: OrderEventType, data: any) {
    this.listeners.forEach(listener => listener(eventType, data));
  }
}

export const orderEventManager = new OrderEventManager();

// Helper functions to convert database rows to our types
const convertToOrder = (
  orderRow: OrderRow,
  orderItems: OrderItemRow[] = [],
  customerName?: string,
  customerEmail?: string,
  branchName?: string
): Order => {
  const items = orderItems.map(item => ({
    id: item.product_id,
    title: 'Product', // Would need to join with products table
    category: 'Category', // Would need to join with categories table
    price: Number(item.unit_price),
    quantity: item.quantity,
    image: '/placeholder.svg' // Would need to join with products table
  }));

  return {
    id: orderRow.id,
    orderNumber: orderRow.order_number,
    customerId: orderRow.customer_id,
    customerName: customerName || 'Unknown Customer',
    customerEmail: customerEmail || '',
    items,
    subtotal: Number(orderRow.subtotal),
    deliveryFee: Number(orderRow.delivery_fee || 0),
    discount: Number(orderRow.discount_amount || 0),
    total: Number(orderRow.total),
    status: orderRow.status as OrderStatus,
    paymentMethod: orderRow.payment_method || 'cash',
    paymentStatus: orderRow.payment_status as PaymentStatus,
    deliveryAddress: typeof orderRow.delivery_address === 'object'
      ? JSON.stringify(orderRow.delivery_address)
      : orderRow.delivery_address || '',
    billingAddress: typeof orderRow.billing_address === 'object'
      ? JSON.stringify(orderRow.billing_address)
      : orderRow.billing_address || '',
    selectedBranch: branchName || 'Unknown Branch',
    promoCode: orderRow.promo_code || undefined,
    notes: orderRow.notes || undefined,
    trackingNumber: orderRow.tracking_number || undefined,
    createdAt: orderRow.created_at,
    updatedAt: orderRow.updated_at,
    estimatedDelivery: orderRow.estimated_delivery || undefined
  };
};

// Enhanced Order Management with database integration
export const getOrders = async (filters?: {
  customerId?: string;
  status?: OrderStatus;
  branchId?: string;
  dateFrom?: string;
  dateTo?: string;
}): Promise<Order[]> => {
  try {
    let query = supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          full_name,
          email
        ),
        branches (
          name
        ),
        order_items (
          *,
          products (
            title,
            featured_image,
            categories (
              name
            )
          )
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (filters?.customerId) {
      query = query.eq('customer_id', filters.customerId);
    }

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.branchId) {
      query = query.eq('branch_id', filters.branchId);
    }

    if (filters?.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }

    if (filters?.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    const { data: orders, error } = await query;

    if (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }

    return orders?.map(order => {
      const items = order.order_items?.map(item => ({
        id: item.product_id,
        title: item.products?.title || 'Unknown Product',
        category: item.products?.categories?.name || 'Uncategorized',
        price: Number(item.unit_price),
        quantity: item.quantity,
        image: item.products?.featured_image || '/placeholder.svg'
      })) || [];

      return convertToOrder(
        order,
        order.order_items || [],
        order.users?.full_name,
        order.users?.email,
        order.branches?.name
      );
    }) || [];
  } catch (error) {
    console.error('Error in getOrders:', error);
    return [];
  }
};

export const createOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt' | 'estimatedDelivery'>): Promise<Order | null> => {
  try {
    const orderNumber = generateOrderId();
    const currentDate = new Date().toISOString();
    const estimatedDelivery = calculateEstimatedDelivery(currentDate);

    // Validate products and check stock availability
    for (const item of orderData.items) {
      const product = await getProductById(item.id.toString());
      if (!product) {
        throw new Error(`Product with ID ${item.id} not found`);
      }
      if (product.stock < item.quantity) {
        throw new Error(`Insufficient stock for product ${product.title}`);
      }
    }

    // Create order in database
    const orderInsert: OrderInsert = {
      order_number: orderNumber,
      customer_id: orderData.customerId,
      branch_id: orderData.selectedBranch, // This would need to be branch ID, not name
      status: orderData.status || 'pending',
      payment_status: orderData.paymentStatus || 'pending',
      payment_method: orderData.paymentMethod,
      subtotal: orderData.subtotal,
      delivery_fee: orderData.deliveryFee || 0,
      discount_amount: orderData.discount || 0,
      total: orderData.total,
      delivery_address: typeof orderData.deliveryAddress === 'string'
        ? { address: orderData.deliveryAddress }
        : orderData.deliveryAddress,
      billing_address: typeof orderData.billingAddress === 'string'
        ? { address: orderData.billingAddress }
        : orderData.billingAddress,
      promo_code: orderData.promoCode,
      notes: orderData.notes,
      estimated_delivery: estimatedDelivery,
    };

    const { data: newOrder, error: orderError } = await supabase
      .from('orders')
      .insert(orderInsert)
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      throw orderError;
    }

    if (!newOrder) {
      throw new Error('Failed to create order');
    }

    // Create order items
    const orderItemsInsert: OrderItemInsert[] = orderData.items.map(item => ({
      order_id: newOrder.id,
      product_id: item.id.toString(),
      quantity: item.quantity,
      unit_price: item.price,
      total_price: item.price * item.quantity,
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItemsInsert);

    if (itemsError) {
      console.error('Error creating order items:', itemsError);
      throw itemsError;
    }

    // Update stock for each item
    for (const item of orderData.items) {
      await updateStock(
        item.id.toString(),
        item.quantity,
        'out',
        `Order ${orderNumber}`,
        newOrder.id,
        orderData.customerId
      );
    }

    // Add initial order tracking
    await addOrderTracking(newOrder.id, 'pending', 'Order created', orderData.customerId);

    // Get the complete order with all relations
    const completeOrder = await getOrderById(newOrder.id);

    if (completeOrder) {
      // Emit real-time event
      orderEventManager.emit('order-created', completeOrder);

      return completeOrder;
    }

    return null;
  } catch (error) {
    console.error('Error in createOrder:', error);
    return null;
  }
};
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    const { data: order, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          full_name,
          email
        ),
        branches (
          name
        ),
        order_items (
          *,
          products (
            title,
            featured_image,
            categories (
              name
            )
          )
        )
      `)
      .eq('id', orderId)
      .single();

    if (error) {
      console.error('Error fetching order:', error);
      return null;
    }

    if (!order) return null;

    const items = order.order_items?.map(item => ({
      id: item.product_id,
      title: item.products?.title || 'Unknown Product',
      category: item.products?.categories?.name || 'Uncategorized',
      price: Number(item.unit_price),
      quantity: item.quantity,
      image: item.products?.featured_image || '/placeholder.svg'
    })) || [];

    return convertToOrder(
      order,
      order.order_items || [],
      order.users?.full_name,
      order.users?.email,
      order.branches?.name
    );
  } catch (error) {
    console.error('Error in getOrderById:', error);
    return null;
  }
};

export const updateOrderStatus = async (orderId: string, status: OrderStatus, updatedBy?: string): Promise<Order | null> => {
  try {
    // Get current order to check old status
    const currentOrder = await getOrderById(orderId);
    if (!currentOrder) return null;

    const oldStatus = currentOrder.status;

    // Update order status in database
    const { data: updatedOrder, error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating order status:', error);
      throw error;
    }

    if (!updatedOrder) return null;

    // Add tracking entry for status change
    const trackingStatus =
      status === 'confirmed' ? 'payment_confirmed' :
      status === 'processing' ? 'preparing' :
      status === 'shipped' ? 'out_for_delivery' :
      status === 'delivered' ? 'delivered' :
      status === 'cancelled' ? 'cancelled' :
      'order_placed'; // default fallback

    await addOrderTracking(
      orderId,
      trackingStatus,
      currentOrder.selectedBranch,
      updatedBy || 'System',
      `Order status changed from ${oldStatus} to ${status}`
    );

    // Get the complete updated order
    const completeOrder = await getOrderById(orderId);

    if (completeOrder) {
      // Emit real-time event
      orderEventManager.emit('order-status-changed', {
        orderId,
        oldStatus,
        newStatus: status,
        order: completeOrder
      });

      return completeOrder;
    }

    return null;
  } catch (error) {
    console.error('Error in updateOrderStatus:', error);
    return null;
  }
};

export const updatePaymentStatus = async (orderId: string, paymentStatus: PaymentStatus, updatedBy?: string): Promise<Order | null> => {
  try {
    // Get current order to check old payment status
    const currentOrder = await getOrderById(orderId);
    if (!currentOrder) return null;

    const oldPaymentStatus = currentOrder.paymentStatus;

    // Update payment status in database
    const { data: updatedOrder, error } = await supabase
      .from('orders')
      .update({ payment_status: paymentStatus })
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }

    if (!updatedOrder) return null;

    // Create payment record if payment is completed
    if (paymentStatus === 'completed') {
      const paymentInsert: PaymentInsert = {
        order_id: orderId,
        amount: currentOrder.total,
        currency: 'MAD',
        method: currentOrder.paymentMethod as any,
        status: 'completed',
        processed_at: new Date().toISOString(),
      };

      const { error: paymentError } = await supabase
        .from('payments')
        .insert(paymentInsert);

      if (paymentError) {
        console.error('Error creating payment record:', paymentError);
        // Don't throw here, order update was successful
      }
    }

    // Get the complete updated order
    const completeOrder = await getOrderById(orderId);

    if (completeOrder) {
      // Emit real-time event
      orderEventManager.emit('payment-updated', {
        orderId,
        oldPaymentStatus,
        newPaymentStatus: paymentStatus,
        order: completeOrder
      });

      return completeOrder;
    }

    return null;
  } catch (error) {
    console.error('Error in updatePaymentStatus:', error);
    return null;
  }
};

export const updateOrder = async (orderId: string, updates: Partial<Order>, updatedBy?: string): Promise<Order | null> => {
  try {
    const orderUpdate: OrderUpdate = {
      delivery_address: typeof updates.deliveryAddress === 'string'
        ? { address: updates.deliveryAddress }
        : updates.deliveryAddress,
      billing_address: typeof updates.billingAddress === 'string'
        ? { address: updates.billingAddress }
        : updates.billingAddress,
      notes: updates.notes,
      tracking_number: updates.trackingNumber,
      estimated_delivery: updates.estimatedDelivery,
    };

    const { data: updatedOrder, error } = await supabase
      .from('orders')
      .update(orderUpdate)
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating order:', error);
      throw error;
    }

    if (!updatedOrder) return null;

    // Get the complete updated order
    const completeOrder = await getOrderById(orderId);

    if (completeOrder) {
      // Emit real-time event
      orderEventManager.emit('order-updated', completeOrder);

      return completeOrder;
    }

    return null;
  } catch (error) {
    console.error('Error in updateOrder:', error);
    return null;
  }
};

export const cancelOrder = async (orderId: string, reason: string, cancelledBy?: string): Promise<Order | null> => {
  try {
    const order = await getOrderById(orderId);
    if (!order) return null;

    // Check if order can be cancelled
    if (order.status === 'delivered' || order.status === 'cancelled') {
      throw new Error(`Cannot cancel order with status: ${order.status}`);
    }

    // Restore stock for cancelled orders
    for (const item of order.items) {
      await updateStock(
        item.id.toString(),
        item.quantity,
        'in',
        `Order ${order.orderNumber || orderId} cancelled: ${reason}`,
        orderId,
        cancelledBy || 'System'
      );
    }

    // Update order status to cancelled
    const cancelledOrder = await updateOrderStatus(orderId, 'cancelled', cancelledBy);

    // Add tracking entry for cancellation
    if (cancelledOrder) {
      await addOrderTracking(
        orderId,
        'cancelled',
        cancelledOrder.selectedBranch,
        cancelledBy || 'System',
        `Order cancelled: ${reason}`
      );
    }

    return cancelledOrder;
  } catch (error) {
    console.error('Error in cancelOrder:', error);
    return null;
  }
};

// Get orders with live product data
export const getOrdersWithLiveData = async (filters?: {
  customerId?: string;
  status?: OrderStatus;
  branchId?: string;
}): Promise<Order[]> => {
  try {
    const ordersList = await getOrders(filters);

    // The database query already includes current product information
    // through joins, so we don't need to fetch it separately
    return ordersList;
  } catch (error) {
    console.error('Error in getOrdersWithLiveData:', error);
    return [];
  }
};

// Get order statistics
export const getOrderStats = async (filters?: {
  dateFrom?: string;
  dateTo?: string;
  branchId?: string;
}): Promise<{
  total: number;
  pending: number;
  confirmed: number;
  processing: number;
  shipped: number;
  delivered: number;
  cancelled: number;
  totalRevenue: number;
}> => {
  try {
    let query = supabase
      .from('orders')
      .select('status, total');

    if (filters?.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }

    if (filters?.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    if (filters?.branchId) {
      query = query.eq('branch_id', filters.branchId);
    }

    const { data: orders, error } = await query;

    if (error) {
      console.error('Error fetching order stats:', error);
      throw error;
    }

    const stats = {
      total: orders?.length || 0,
      pending: 0,
      confirmed: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
      totalRevenue: 0,
    };

    orders?.forEach(order => {
      stats[order.status as keyof typeof stats]++;
      if (order.status !== 'cancelled') {
        stats.totalRevenue += Number(order.total);
      }
    });

    return stats;
  } catch (error) {
    console.error('Error in getOrderStats:', error);
    return {
      total: 0,
      pending: 0,
      confirmed: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
      totalRevenue: 0,
    };
  }
};
