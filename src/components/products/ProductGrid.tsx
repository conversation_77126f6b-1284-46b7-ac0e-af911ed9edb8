
import { useState, useEffect } from 'react';
import { Product, Category } from '../../types/inventory';
import { getProducts, getCategories } from '../../services/inventoryService';
import ProductCard from './ProductCard';
import { Slider } from "@/components/ui/slider"

export interface SearchFilters {
  searchTerm: string;
  category: string;
  priceRange: [number, number];
  minRating: number;
  inStock: boolean;
  isNew: boolean;
  brand: string;
  sortBy: 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest' | 'popularity';
  availability: string;
  discount: boolean;
  freeShipping: boolean;
}

interface ProductGridProps {
  viewMode?: 'grid' | 'list';
  searchFilters?: SearchFilters;
  userType?: string;
  customerId?: string;
  onAddToCart?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
}

const ProductGrid = ({
  viewMode = 'grid',
  searchFilters,
  userType = 'client',
  customerId,
  onAddToCart,
  onAddToWishlist
}: ProductGridProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchTerm, setSearchTerm] = useState(searchFilters?.searchTerm || '');
  const [selectedCategory, setSelectedCategory] = useState(searchFilters?.category || '');
  const [priceRange, setPriceRange] = useState<[number, number]>(searchFilters?.priceRange || [0, 1000]);
  const [sortBy, setSortBy] = useState<'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest' | 'popularity'>(searchFilters?.sortBy || 'relevance');
  const [stockFilter, setStockFilter] = useState<'all' | 'in-stock' | 'low-stock' | 'out-of-stock'>('all');
  const [loading, setLoading] = useState(true);

  // Update local state when searchFilters prop changes
  useEffect(() => {
    if (searchFilters) {
      setSearchTerm(searchFilters.searchTerm);
      setSelectedCategory(searchFilters.category);
      setPriceRange(searchFilters.priceRange);
      setSortBy(searchFilters.sortBy);
    }
  }, [searchFilters]);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterAndSortProducts();
  }, [products, searchTerm, selectedCategory, priceRange, sortBy, stockFilter]);

  const loadData = async () => {
    setLoading(true);
    try {
      const productsData = await getProducts();
      const categoriesData = await getCategories();
      setProducts(productsData);
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortProducts = () => {
    let filtered = [...products];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Filter by price range
    filtered = filtered.filter(product => 
      product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    // Filter by stock status
    if (stockFilter === 'in-stock') {
      filtered = filtered.filter(product => product.stock > product.minStock);
    } else if (stockFilter === 'out-of-stock') {
      filtered = filtered.filter(product => product.stock === 0);
    } else if (stockFilter === 'low-stock') {
      filtered = filtered.filter(product => product.stock > 0 && product.stock <= product.minStock);
    }

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'popularity':
          return (b.rating || 0) - (a.rating || 0); // Using rating as popularity proxy
        case 'relevance':
        default:
          return a.title.localeCompare(b.title);
      }
    });

    setFilteredProducts(filtered);
  };

  const handleAddToCart = (product: Product) => {
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  if (loading) {
    return <div>Loading products...</div>;
  }

  return (
    <div>
      {/* Search and Filters - only show if not controlled by parent */}
      {!searchFilters && (
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search products..."
            className="p-2 border rounded mr-2"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            className="p-2 border rounded mr-2"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.name}>{category.name}</option>
            ))}
          </select>
          <select
            className="p-2 border rounded mr-2"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
          >
            <option value="relevance">Relevance</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Rating</option>
            <option value="newest">Newest</option>
            <option value="popularity">Popularity</option>
          </select>
          <select
            className="p-2 border rounded"
            value={stockFilter}
            onChange={(e) => setStockFilter(e.target.value as any)}
          >
            <option value="all">All Stock</option>
            <option value="in-stock">In Stock</option>
            <option value="low-stock">Low Stock</option>
            <option value="out-of-stock">Out of Stock</option>
          </select>
        </div>
      )}

      {/* Price Range Slider - only show if not controlled by parent */}
      {!searchFilters && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700">Price Range: {priceRange[0]} - {priceRange[1]}</label>
          <Slider
            defaultValue={[priceRange[0], priceRange[1]]}
            max={1000}
            step={10}
            onValueChange={(value) => setPriceRange(value as [number, number])}
          />
        </div>
      )}

      {/* Product Grid */}
      <div className={viewMode === 'grid' 
        ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
        : "space-y-4"
      }>
        {filteredProducts.map(product => (
          <ProductCard
            key={product.id}
            product={product}
            userType={userType}
            customerId={customerId}
            onAddToCart={handleAddToCart}
            onAddToWishlist={onAddToWishlist}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductGrid;
