
import { useState, useEffect } from 'react';
import { Eye, Star, ShoppingCart, Heart } from 'lucide-react';
import { Product } from '../../types/inventory';
import { formatPrice } from '../../utils/inventoryUtils';
import ProductDetailsModal from './ProductDetailsModal';
import { useWishlist } from '../../hooks/useWishlist';

interface ProductCardProps {
  product: Product;
  userType?: string;
  customerId?: string;
  onAddToCart?: (product: Product) => void;
  onAddToWishlist?: (product: Product) => void;
}

const ProductCard = ({ product, userType = 'client', customerId, onAddToCart, onAddToWishlist }: ProductCardProps) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Use wishlist hook if customerId is provided
  const wishlist = customerId ? useWishlist(customerId) : null;
  const isWishlisted = wishlist ? wishlist.isInWishlist(product.id) : false;

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  const handleAddToWishlist = async () => {
    if (wishlist && customerId) {
      try {
        if (isWishlisted) {
          await wishlist.removeFromWishlist(product.id);
        } else {
          await wishlist.addToWishlist(product.id);
        }
      } catch (error) {
        console.error('Error updating wishlist:', error);
      }
    } else if (onAddToWishlist) {
      // Fallback to callback if no wishlist hook
      onAddToWishlist(product);
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative">
          <img
            src={product.featuredImage || product.image}
            alt={product.title}
            className="w-full h-48 object-cover"
          />
          <button
            onClick={() => setShowDetailsModal(true)}
            className="absolute top-3 right-3 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-700 p-2 rounded-full shadow-md transition-all"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </button>
        </div>
        
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.title}</h3>
          <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
          <p className="text-sm text-gray-500 mb-3 line-clamp-2">{product.description}</p>
          
          <div className="flex justify-between items-center mb-3">
            <span className="text-xl font-bold text-gray-900">{formatPrice(product.price)}</span>
            {product.rating && (
              <div className="flex items-center">
                <span className="text-yellow-400">★</span>
                <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
              </div>
            )}
          </div>
          
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm text-gray-500">Stock: {product.stock}</span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              product.stock > product.minStock 
                ? 'bg-green-100 text-green-800' 
                : product.stock > 0 
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-red-100 text-red-800'
            }`}>
              {product.stock > product.minStock 
                ? 'In Stock' 
                : product.stock > 0 
                  ? 'Low Stock' 
                  : 'Out of Stock'
              }
            </span>
          </div>
          
          {/* Three Action Buttons in a Single Row */}
          <div className="flex space-x-2">
            <button
              className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center space-x-1 ${
                product.stock === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-teal-500 to-teal-600 text-white hover:from-teal-600 hover:to-teal-700'
              }`}
              disabled={product.stock === 0}
              onClick={handleAddToCart}
              title={product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
            >
              <ShoppingCart className="h-4 w-4" />
              <span className="text-sm font-medium">
                {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
              </span>
            </button>

            <button
              onClick={() => setShowDetailsModal(true)}
              className="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center space-x-1"
              title="View Details"
            >
              <Eye className="h-4 w-4" />
              <span className="text-sm font-medium">View</span>
            </button>

            <button
              onClick={handleAddToWishlist}
              className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center space-x-1 ${
                isWishlisted
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-amber-100 text-amber-700 hover:bg-amber-200'
              }`}
              title={isWishlisted ? 'Remove from Wishlist' : 'Add to Wishlist'}
            >
              <Heart className={`h-4 w-4 ${isWishlisted ? 'fill-current' : ''}`} />
              <span className="text-sm font-medium">Wishlist</span>
            </button>
          </div>
        </div>
      </div>

      {showDetailsModal && (
        <ProductDetailsModal
          productId={product.id}
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          userType={userType as 'client' | 'reseller'}
          onAddToCart={onAddToCart}
        />
      )}
    </>
  );
};

export default ProductCard;
