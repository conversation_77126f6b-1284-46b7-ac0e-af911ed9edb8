import { useState, useEffect } from 'react';
import { X, Star, ShoppingCart, Package, Truck, Shield, Heart, ZoomIn, ChevronLeft, ChevronRight, MessageCircle, ThumbsUp } from 'lucide-react';
import { Product } from '../../types/inventory';
import { getProductById } from '../../services/inventoryService';
import ImageService from '../../services/imageService';
import { realTimeService } from '../../services/realTimeService';
import { reviewService, ProductReview, ReviewStats } from '../../services/reviewService';
import { useWishlist } from '../../hooks/useWishlist';

interface ProductDetailsModalProps {
  productId: string | null;
  isOpen: boolean;
  onClose: () => void;
  userType: 'client' | 'reseller';
  customerId?: string;
  customerName?: string;
  onAddToCart?: (product: any) => void;
}

const ProductDetailsModal = ({ productId, isOpen, onClose, userType, customerId, customerName, onAddToCart }: ProductDetailsModalProps) => {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isZoomed, setIsZoomed] = useState(false);

  // Review and rating states
  const [reviews, setReviews] = useState<ProductReview[]>([]);
  const [reviewStats, setReviewStats] = useState<ReviewStats>({ averageRating: 0, totalReviews: 0, ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 } });
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newReview, setNewReview] = useState({ rating: 5, title: '', comment: '' });
  const [hasUserReviewed, setHasUserReviewed] = useState(false);

  // Wishlist hook
  const wishlist = customerId ? useWishlist(customerId) : null;
  const isWishlisted = wishlist ? wishlist.isInWishlist(productId || '') : false;

  useEffect(() => {
    if (isOpen && productId) {
      loadProduct();
      loadReviews();
      checkUserReview();
    }
  }, [isOpen, productId]);

  useEffect(() => {
    if (!isOpen) return;

    // Subscribe to real-time product updates
    const unsubscribe = realTimeService.subscribe('product-updated', (event) => {
      if (event.data.productId === productId || event.data.id === productId) {
        setProduct(event.data.newData || event.data);
      }
    });

    const unsubscribeImage = realTimeService.subscribe('image-updated', (event) => {
      if (event.data.productId === productId) {
        setProduct(prev => prev ? {
          ...prev,
          featuredImage: event.data.featuredImage,
          thumbnailImages: event.data.thumbnailImages
        } : null);
      }
    });

    return () => {
      unsubscribe();
      unsubscribeImage();
    };
  }, [isOpen, productId]);

  const loadProduct = async () => {
    if (!productId) return;

    try {
      setLoading(true);
      setError(null);
      const productData = await getProductById(productId);
      if (productData) {
        setProduct(productData);
        setSelectedImageIndex(0);
      } else {
        setError('Product not found');
      }
    } catch (err) {
      setError('Failed to load product details');
      console.error('Error loading product:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadReviews = async () => {
    if (!productId) return;

    try {
      const [reviewsData, statsData] = await Promise.all([
        reviewService.getProductReviews(productId, 10),
        reviewService.getReviewStats(productId)
      ]);

      setReviews(reviewsData);
      setReviewStats(statsData);
    } catch (err) {
      console.error('Error loading reviews:', err);
    }
  };

  const checkUserReview = async () => {
    if (!productId || !customerId) {
      setHasUserReviewed(false);
      return;
    }

    try {
      const hasReviewed = await reviewService.hasUserReviewed(productId, customerId);
      setHasUserReviewed(hasReviewed);
    } catch (err) {
      console.error('Error checking user review:', err);
      setHasUserReviewed(false);
    }
  };

  const handleAddToCart = () => {
    if (product && onAddToCart) {
      const cartProduct = {
        id: parseInt(product.id.replace('PRD-', '')),
        title: product.title,
        category: product.category,
        price: product.price,
        resellerPrice: product.resellerPrice,
        image: product.featuredImage,
        rating: product.rating,
        inStock: product.stock > 0,
        stock: product.stock
      };

      for (let i = 0; i < quantity; i++) {
        onAddToCart(cartProduct);
      }

      // Show success feedback
      console.log(`Added ${quantity} ${product.title} to cart`);
    }
  };

  const handleWishlistToggle = async () => {
    if (!wishlist || !productId) return;

    try {
      if (isWishlisted) {
        await wishlist.removeFromWishlist(productId);
      } else {
        await wishlist.addToWishlist(productId);
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
    }
  };

  const handleSubmitReview = async () => {
    if (!productId || !customerId || !customerName) {
      alert('Please log in to submit a review.');
      return;
    }

    if (!newReview.comment.trim()) {
      alert('Please enter a comment for your review.');
      return;
    }

    try {
      await reviewService.createReview({
        product_id: productId,
        customer_id: customerId,
        customer_name: customerName,
        rating: newReview.rating,
        title: newReview.title.trim(),
        comment: newReview.comment.trim()
      });

      // Reset form and reload reviews
      setNewReview({ rating: 5, title: '', comment: '' });
      setShowReviewForm(false);
      setHasUserReviewed(true);
      await loadReviews();

      // Show success message
      alert('Review submitted successfully!');
    } catch (error) {
      console.error('Error submitting review:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit review. Please try again.';
      alert(errorMessage);
    }
  };

  const handleMarkHelpful = async (reviewId: string) => {
    try {
      await reviewService.markReviewHelpful(reviewId);
      await loadReviews(); // Reload to show updated helpful count
    } catch (error) {
      console.error('Error marking review as helpful:', error);
    }
  };

  const handleImageSelect = (index: number) => {
    setSelectedImageIndex(index);
  };

  const handlePrevImage = () => {
    if (product) {
      const allImages = ImageService.getAllProductImages(product);
      setSelectedImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length);
    }
  };

  const handleNextImage = () => {
    if (product) {
      const allImages = ImageService.getAllProductImages(product);
      setSelectedImageIndex((prev) => (prev + 1) % allImages.length);
    }
  };

  const formatPrice = (price: number) => {
    return `${price.toFixed(2)} Dh`;
  };

  if (!isOpen) return null;

  const allImages = product ? ImageService.getAllProductImages(product) : [];
  const displayPrice = product && userType === 'reseller' ? product.resellerPrice : product?.price || 0;
  const savings = product && userType === 'reseller' ? product.price - product.resellerPrice : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Product Details</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-gray-500" />
          </button>
        </div>

        {loading && (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        )}

        {error && (
          <div className="p-8 text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={loadProduct}
              className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {product && !loading && !error && (
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image Gallery */}
              <div className="space-y-4">
                {/* Main Image */}
                <div className="relative bg-gray-100 rounded-xl overflow-hidden aspect-square">
                  <img
                    src={allImages[selectedImageIndex] || ImageService.generateFallbackImage(product.title, 400)}
                    alt={product.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = ImageService.generateFallbackImage(product.title, 400);
                    }}
                  />
                  
                  {/* Navigation Arrows */}
                  {allImages.length > 1 && (
                    <>
                      <button
                        onClick={handlePrevImage}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 transition-all"
                      >
                        <ChevronLeft className="h-5 w-5 text-gray-700" />
                      </button>
                      <button
                        onClick={handleNextImage}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 transition-all"
                      >
                        <ChevronRight className="h-5 w-5 text-gray-700" />
                      </button>
                    </>
                  )}

                  {/* Zoom Button */}
                  <button
                    onClick={() => setIsZoomed(true)}
                    className="absolute top-2 right-2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 transition-all"
                    title="Zoom Image"
                  >
                    <ZoomIn className="h-4 w-4 text-gray-700" />
                  </button>
                </div>

                {/* Thumbnail Images */}
                {allImages.length > 1 && (
                  <div className="flex space-x-2 overflow-x-auto">
                    {allImages.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => handleImageSelect(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                          selectedImageIndex === index
                            ? 'border-orange-500 ring-2 ring-orange-200'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <img
                          src={image || ImageService.generateFallbackImage(product.title, 64)}
                          alt={`${product.title} ${index + 1}`}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = ImageService.generateFallbackImage(product.title, 64);
                          }}
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Information */}
              <div className="space-y-6">
                {/* Title, Brand and Rating */}
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.title}</h1>

                  {/* Brand Information */}
                  {product.brand && (
                    <div className="mb-2">
                      <span className="text-sm text-gray-500">Brand: </span>
                      <span className="text-sm font-medium text-gray-700">{product.brand}</span>
                    </div>
                  )}

                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-5 w-5 ${
                            i < Math.floor(reviewStats.averageRating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-gray-600">
                      ({reviewStats.averageRating.toFixed(1)}) • {reviewStats.totalReviews} reviews
                    </span>
                  </div>
                  <p className="text-gray-600">{product.description}</p>
                </div>

                {/* Price */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatPrice(displayPrice)}
                    </span>
                    {userType === 'reseller' && savings > 0 && (
                      <span className="text-lg text-gray-500 line-through">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>
                  
                  {userType === 'reseller' && savings > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium">
                        Save {formatPrice(savings)}
                      </span>
                      <span className="text-sm text-gray-600">Reseller Price</span>
                    </div>
                  )}
                </div>

                {/* Stock Status */}
                <div className="flex items-center space-x-2">
                  <Package className="h-5 w-5 text-gray-500" />
                  <span className={`font-medium ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                  </span>
                </div>

                {/* Quantity and Add to Cart */}
                {product.stock > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <label className="text-sm font-medium text-gray-700">Quantity:</label>
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <button
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                          className="px-3 py-2 hover:bg-gray-100 transition-colors"
                        >
                          -
                        </button>
                        <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                        <button
                          onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
                          className="px-3 py-2 hover:bg-gray-100 transition-colors"
                        >
                          +
                        </button>
                      </div>
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={handleAddToCart}
                        className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all font-semibold flex items-center justify-center space-x-2"
                      >
                        <ShoppingCart className="h-5 w-5" />
                        <span>Add to Cart</span>
                      </button>
                      
                      {customerId && (
                        <button
                          onClick={handleWishlistToggle}
                          className={`px-4 py-3 rounded-lg border transition-all flex items-center space-x-2 ${
                            isWishlisted
                              ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                              : 'bg-amber-50 border-amber-200 text-amber-600 hover:bg-amber-100'
                          }`}
                          title={isWishlisted ? 'Remove from Wishlist' : 'Add to Wishlist'}
                        >
                          <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
                          <span className="text-sm font-medium">
                            {isWishlisted ? 'Remove from Wishlist' : 'Add to Wishlist'}
                          </span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Reviews and Comments Section */}
            <div className="mt-8 border-t border-gray-200 pt-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Reviews & Comments</h3>
                {customerId && !hasUserReviewed && (
                  <button
                    onClick={() => setShowReviewForm(!showReviewForm)}
                    className="bg-gradient-to-r from-teal-500 to-teal-600 text-white px-4 py-2 rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all text-sm font-medium"
                  >
                    Write a Review
                  </button>
                )}
              </div>

              {/* Review Form */}
              {showReviewForm && (
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Write Your Review</h4>

                  {/* Star Rating Input */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
                    <div className="flex items-center space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          onClick={() => setNewReview(prev => ({ ...prev, rating: star }))}
                          className="focus:outline-none"
                        >
                          <Star
                            className={`h-6 w-6 ${
                              star <= newReview.rating
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            } hover:text-yellow-400 transition-colors`}
                          />
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Review Title */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Title (Optional)</label>
                    <input
                      type="text"
                      value={newReview.title}
                      onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
                      placeholder="Brief summary of your review"
                    />
                  </div>

                  {/* Review Comment */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Comment</label>
                    <textarea
                      value={newReview.comment}
                      onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                      rows={4}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500"
                      placeholder="Share your experience with this product..."
                      required
                    />
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex space-x-3">
                    <button
                      onClick={handleSubmitReview}
                      disabled={!newReview.comment.trim()}
                      className="bg-gradient-to-r from-teal-500 to-teal-600 text-white px-4 py-2 rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Submit Review
                    </button>
                    <button
                      onClick={() => setShowReviewForm(false)}
                      className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Reviews List */}
              <div className="space-y-6">
                {reviews.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
                  </div>
                ) : (
                  reviews.map((review) => (
                    <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900">{review.customer_name}</span>
                            {review.is_verified && (
                              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                Verified Purchase
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="text-sm text-gray-500">
                              {new Date(review.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>

                      {review.title && (
                        <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
                      )}

                      <p className="text-gray-700 mb-3">{review.comment}</p>

                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleMarkHelpful(review.id)}
                          className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                        >
                          <ThumbsUp className="h-4 w-4" />
                          <span>Helpful ({review.helpful_count})</span>
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {/* Image Zoom Modal */}
        {isZoomed && (
          <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-60 p-4">
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setIsZoomed(false)}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <X className="h-8 w-8" />
              </button>
              <img
                src={allImages[selectedImageIndex] || ImageService.generateFallbackImage(product?.title || '', 800)}
                alt={product?.title}
                className="max-w-full max-h-full object-contain"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailsModal;
