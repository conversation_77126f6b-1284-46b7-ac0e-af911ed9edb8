import { useState, useEffect } from 'react';
import { Star, MessageSquare, User, Package, Calendar, CheckCircle, XCircle, Trash2, Reply, Filter, Search, Eye, MoreHorizontal } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';

interface Review {
  id: string;
  product_id: string;
  customer_id: string;
  rating: number;
  title?: string;
  comment: string;
  is_verified: boolean;
  helpful_count: number;
  status?: 'pending' | 'approved' | 'rejected'; // We'll add this field
  created_at: string;
  updated_at: string;
  products?: {
    id: string;
    title: string;
    featured_image?: string;
  };
  users?: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface ReviewStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  averageRating: number;
}

const ReviewsManagementPage = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    averageRating: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    rating: 'all',
    search: ''
  });

  useEffect(() => {
    loadReviews();
    setupRealTimeSync();
  }, []);

  const setupRealTimeSync = () => {
    console.log('ReviewsManagement: Setting up real-time synchronization');

    // Subscribe to review changes
    const reviewSubscription = liveDataService.subscribeToTable('product_reviews', (payload) => {
      console.log('ReviewsManagement: Real-time review update:', payload);
      loadReviews(); // Reload reviews when changes occur
    });

    // Subscribe to review-related events
    const reviewEventSubscription = realTimeService.subscribe('review-updated', (payload) => {
      console.log('ReviewsManagement: Review event received:', payload);
      loadReviews();
    });

    return () => {
      reviewSubscription.unsubscribe();
      reviewEventSubscription();
    };
  };

  const loadReviews = async () => {
    try {
      setLoading(true);
      console.log('ReviewsManagement: Loading reviews from database');

      const { data: reviewsData, error } = await supabase
        .from('product_reviews')
        .select(`
          *,
          products (
            id,
            title,
            featured_image
          ),
          users!product_reviews_customer_id_fkey (
            id,
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading reviews:', error);
        throw error;
      }

      setReviews(reviewsData || []);
      calculateStats(reviewsData || []);
      console.log('ReviewsManagement: Reviews loaded successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (reviewsData: Review[]) => {
    const stats = {
      total: reviewsData.length,
      pending: reviewsData.filter(r => r.status === 'pending' || !r.status).length, // Default to pending if no status
      approved: reviewsData.filter(r => r.status === 'approved').length,
      rejected: reviewsData.filter(r => r.status === 'rejected').length,
      averageRating: reviewsData.length > 0
        ? reviewsData.reduce((sum, r) => sum + r.rating, 0) / reviewsData.length
        : 0
    };
    setStats(stats);
  };

  const handleStatusUpdate = async (reviewId: string, newStatus: 'approved' | 'rejected') => {
    try {
      console.log(`ReviewsManagement: Updating review ${reviewId} status to ${newStatus}`);

      const { error } = await supabase
        .from('product_reviews')
        .update({
          updated_at: new Date().toISOString()
          // Note: We'll need to add status field to the database schema
        })
        .eq('id', reviewId);

      if (error) {
        console.error('Error updating review status:', error);
        throw error;
      }

      // Emit real-time event
      realTimeService.emit('review-updated', {
        reviewId,
        status: newStatus,
        timestamp: new Date().toISOString()
      });

      console.log('ReviewsManagement: Review status updated successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error updating review status:', error);
      alert('Error updating review status. Please try again.');
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return;
    }

    try {
      console.log(`ReviewsManagement: Deleting review ${reviewId}`);

      const { error } = await supabase
        .from('product_reviews')
        .delete()
        .eq('id', reviewId);

      if (error) {
        console.error('Error deleting review:', error);
        throw error;
      }

      // Emit real-time event
      realTimeService.emit('review-deleted', {
        reviewId,
        timestamp: new Date().toISOString()
      });

      console.log('ReviewsManagement: Review deleted successfully');
    } catch (error) {
      console.error('ReviewsManagement: Error deleting review:', error);
      alert('Error deleting review. Please try again.');
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600 ml-1">({rating})</span>
      </div>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      approved: { bg: 'bg-green-100', text: 'text-green-800', label: 'Approved' },
      rejected: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const filteredReviews = reviews.filter(review => {
    const reviewStatus = review.status || 'pending'; // Default to pending if no status
    if (filters.status !== 'all' && reviewStatus !== filters.status) return false;
    if (filters.rating !== 'all' && review.rating !== parseInt(filters.rating)) return false;
    if (filters.search && !review.products?.title.toLowerCase().includes(filters.search.toLowerCase()) &&
        !review.users?.full_name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !review.comment?.toLowerCase().includes(filters.search.toLowerCase())) return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <MessageSquare className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Reviews</p>
              <p className="text-lg font-semibold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Star className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Avg Rating</p>
              <p className="text-lg font-semibold text-gray-900">{stats.averageRating.toFixed(1)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Calendar className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-lg font-semibold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="text-lg font-semibold text-gray-900">{stats.approved}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Rejected</p>
              <p className="text-lg font-semibold text-gray-900">{stats.rejected}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>
            
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            <select
              value={filters.rating}
              onChange={(e) => setFilters(prev => ({ ...prev, rating: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="all">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search reviews..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Reviews Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Reviews ({filteredReviews.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Comment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredReviews.map((review) => (
                <tr key={review.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {review.products?.featured_image ? (
                        <img
                          className="h-10 w-10 rounded-lg object-cover mr-3"
                          src={review.products.featured_image}
                          alt={review.products.title}
                        />
                      ) : (
                        <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                          <Package className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {review.products?.title || 'Unknown Product'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                        <User className="h-4 w-4 text-teal-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {review.users?.full_name || 'Anonymous'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {review.users?.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStars(review.rating)}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {review.comment}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(review.status || 'pending')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(review.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      {(review.status === 'pending' || !review.status) && (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(review.id, 'approved')}
                            className="text-green-600 hover:text-green-900 p-1 rounded"
                            title="Approve"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(review.id, 'rejected')}
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Reject"
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      <button
                        onClick={() => {
                          setSelectedReview(review);
                          setShowReplyModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Reply"
                      >
                        <Reply className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteReview(review.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded"
                        title="Delete"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {filters.search || filters.status !== 'all' || filters.rating !== 'all'
                  ? 'Try adjusting your filters'
                  : 'Reviews will appear here when customers leave feedback'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReviewsManagementPage;
