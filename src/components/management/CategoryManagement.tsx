import { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, FolderPlus, Folder, ChevronRight, ChevronDown } from 'lucide-react';
import { Category } from '../../types/management';
import { getCategories, createCategory, updateCategory, deleteCategory, getCategoryHierarchy } from '../../services/liveCategoryService';
import { realTimeService } from '../../services/realTimeService';
import YalaPagination from '../ui/YalaPagination';

interface CategoryManagementProps {
  userType: string;
  userId: string;
}

const CategoryManagement = ({ userType, userId }: CategoryManagementProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [hierarchicalCategories, setHierarchicalCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadCategories();
    
    // Subscribe to real-time category updates
    const unsubscribeCreated = realTimeService.subscribe('category-created', handleCategoryCreated);
    const unsubscribeUpdated = realTimeService.subscribe('category-updated', handleCategoryUpdated);
    const unsubscribeDeleted = realTimeService.subscribe('category-deleted', handleCategoryDeleted);
    
    return () => {
      unsubscribeCreated();
      unsubscribeUpdated();
      unsubscribeDeleted();
    };
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const [flatCategories, hierarchical] = await Promise.all([
        getCategories(),
        getCategoryHierarchy()
      ]);
      setCategories(flatCategories);
      setHierarchicalCategories(hierarchical);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryCreated = (event: any) => {
    setCategories(prev => [...prev, event.data.category]);
    loadCategories(); // Reload to update hierarchy
  };

  const handleCategoryUpdated = (event: any) => {
    setCategories(prev => prev.map(cat => 
      cat.id === event.data.categoryId ? event.data.newData : cat
    ));
    loadCategories(); // Reload to update hierarchy
  };

  const handleCategoryDeleted = (event: any) => {
    setCategories(prev => prev.filter(cat => cat.id !== event.data.categoryId));
    loadCategories(); // Reload to update hierarchy
  };

  const handleCreateCategory = async (categoryData: any) => {
    try {
      await createCategory(categoryData, userId);
      setShowCategoryForm(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error('Error creating category:', error);
    }
  };

  const handleUpdateCategory = async (categoryData: any) => {
    if (!selectedCategory) return;
    
    try {
      await updateCategory(selectedCategory.id, categoryData, userId);
      setShowCategoryForm(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error('Error updating category:', error);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await deleteCategory(categoryId, userId);
      setShowDeleteConfirm(null);
    } catch (error) {
      console.error('Error deleting category:', error);
      alert(error.message);
    }
  };

  const toggleExpanded = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const renderCategoryTree = (categories: any[], level = 0) => {
    return categories.map((category) => (
      <div key={category.id} className="border-b border-gray-100 last:border-b-0">
        <div 
          className={`flex items-center justify-between p-3 hover:bg-gray-50 ${level > 0 ? 'ml-' + (level * 6) : ''}`}
          style={{ paddingLeft: `${level * 24 + 12}px` }}
        >
          <div className="flex items-center space-x-3">
            {category.children && category.children.length > 0 ? (
              <button
                onClick={() => toggleExpanded(category.id)}
                className="text-gray-400 hover:text-gray-600"
              >
                {expandedCategories.has(category.id) ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>
            ) : (
              <div className="w-4" />
            )}
            
            <Folder className="h-5 w-5 text-gray-400" />
            
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900">{category.name}</span>
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {category.productCount} products
                </span>
                {!category.isActive && (
                  <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                    Inactive
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500">{category.description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                setSelectedCategory(category);
                setShowCategoryForm(true);
              }}
              className="text-orange-600 hover:text-orange-900"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={() => setShowDeleteConfirm(category.id)}
              className="text-red-600 hover:text-red-900"
              disabled={category.productCount > 0}
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {category.children && category.children.length > 0 && expandedCategories.has(category.id) && (
          <div>
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const filteredCategories = hierarchicalCategories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Category Management</h2>
          <p className="text-gray-600">Organize your product categories</p>
        </div>
        <button
          onClick={() => {
            setSelectedCategory(null);
            setShowCategoryForm(true);
          }}
          className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
        >
          <FolderPlus className="h-5 w-5" />
          <span>Add Category</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-semibold text-gray-900">{categories.length}</div>
          <div className="text-sm text-gray-500">Total Categories</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-semibold text-gray-900">
            {categories.filter(c => c.isActive).length}
          </div>
          <div className="text-sm text-gray-500">Active Categories</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-semibold text-gray-900">
            {categories.filter(c => c.level === 0).length}
          </div>
          <div className="text-sm text-gray-500">Main Categories</div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="text-2xl font-semibold text-gray-900">
            {categories.filter(c => c.productCount > 0).length}
          </div>
          <div className="text-sm text-gray-500">With Products</div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Categories Tree */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {filteredCategories.length > 0 ? (
            renderCategoryTree(filteredCategories)
          ) : (
            <div className="p-8 text-center text-gray-500">
              No categories found
            </div>
          )}
        </div>
      </div>

      {/* Category Form Modal */}
      {showCategoryForm && (
        <CategoryForm
          category={selectedCategory}
          categories={categories}
          onSubmit={selectedCategory ? handleUpdateCategory : handleCreateCategory}
          onCancel={() => {
            setShowCategoryForm(false);
            setSelectedCategory(null);
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Category</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this category? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDeleteCategory(showDeleteConfirm)}
                className="flex-1 bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Category Form Component
const CategoryForm = ({ category, categories, onSubmit, onCancel }: any) => {
  const [formData, setFormData] = useState({
    name: category?.name || '',
    description: category?.description || '',
    parentId: category?.parentId || '',
    isActive: category?.isActive ?? true,
    sortOrder: category?.sortOrder || 1,
    icon: category?.icon || '',
    color: category?.color || '#3B82F6'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const parentCategories = categories.filter((cat: Category) => 
    cat.level === 0 && cat.id !== category?.id
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {category ? 'Edit Category' : 'Add Category'}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              rows={3}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Parent Category</label>
            <select
              value={formData.parentId}
              onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">None (Main Category)</option>
              {parentCategories.map((cat: Category) => (
                <option key={cat.id} value={cat.id}>{cat.name}</option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">Active</label>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors"
            >
              {category ? 'Update' : 'Create'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CategoryManagement;
