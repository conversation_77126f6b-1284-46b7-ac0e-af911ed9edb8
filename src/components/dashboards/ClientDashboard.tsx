import { useState, useEffect } from 'react';
import { ShoppingCart, Package, Star, Filter, Search, Grid, List, Bell, User, Shield, Heart } from 'lucide-react';
import ProductGrid from '../products/ProductGrid';
import Cart from '../cart/Cart';
import OrderHistory from '../orders/OrderHistory';
import ProfileManagement from '../profile/ProfileManagement';
import NotificationBell from '../ui/NotificationBell';
import AdvancedSearch, { SearchFilters } from '../search/AdvancedSearch';
import RecentlyViewed from '../search/RecentlyViewed';
import SecurityDashboard from '../security/SecurityDashboard';
import BestSellingProducts from '../dashboard/BestSellingProducts';
import WishlistDashboard from '../wishlist/WishlistDashboard';
import { getActiveCategories } from '../../services/liveCategoryService';
import { liveDataService } from '../../services/liveDataService';

interface ClientDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
}

const ClientDashboard = ({ user: initialUser, onNavigateFromHeader }: ClientDashboardProps) => {
  const [user, setUser] = useState(initialUser);

  // Initialize state from URL parameters and localStorage
  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const storedTab = localStorage.getItem('client_active_tab');
    return urlTab || storedTab || 'products';
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [cart, setCart] = useState<any[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // Search and filter states
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchTerm: '',
    category: '',
    priceRange: [0, 1000],
    minRating: 0,
    inStock: false,
    isNew: false,
    brand: '',
    sortBy: 'relevance',
    availability: 'all',
    discount: false,
    freeShipping: false
  });

  // Live categories from database
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  const brands = ['Pilot', 'BIC', 'Staedtler', 'Faber-Castell', 'Parker', 'Sharpie', 'Post-it', 'Moleskine'];

  // Load categories on mount and set up real-time subscriptions
  useEffect(() => {
    loadCategories();

    // Subscribe to real-time updates
    const categorySubscription = liveDataService.subscribeToCategories((payload) => {
      console.log('Real-time category update in ClientDashboard:', payload);
      loadCategories();
    });

    const productSubscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Real-time product update in ClientDashboard:', payload);
      // Products will be handled by individual components
    });

    const orderSubscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Real-time order update in ClientDashboard:', payload);
      // Orders will be handled by OrderHistory component
    });

    return () => {
      categorySubscription.unsubscribe();
      productSubscription.unsubscribe();
      orderSubscription.unsubscribe();
    };
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const categoriesData = await getActiveCategories();
      const categoryNames = categoriesData.map(cat => cat.name);
      setCategories(categoryNames);
    } catch (error) {
      console.error('Error loading categories:', error);
      // Fallback to hardcoded categories if live data fails
      setCategories([
        'Writing Instruments',
        'Paper & Notebooks',
        'Office & Desk Accessories',
        'Technology',
        'Storage & Organization',
        'Art & Craft Supplies',
        'Printing & Copying',
        'Furniture',
        'School & Office Supplies',
        'Filing & Organization',
        'Greeting Cards & Gift Supplies',
        'Back-to-School Essentials',
        'Eco-Friendly Stationery',
        'Specialty & Luxury Stationery'
      ]);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (product: any) => {
    try {
      // Update local state immediately for better UX
      setCart(prev => {
        const existing = prev.find(item => item.id === product.id);
        if (existing) {
          return prev.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          );
        }
        return [...prev, { ...product, quantity: 1 }];
      });

      // Sync with database for persistence across sessions
      // This would typically save to a user_carts table in Supabase
      if (user.id) {
        await liveDataService.syncCartItem(user.id, product.id, 1);
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      // Could show a toast notification here
    }
  };

  const updateCartQuantity = async (productId: number, quantity: number) => {
    try {
      // Update local state immediately
      if (quantity === 0) {
        setCart(prev => prev.filter(item => item.id !== productId));
      } else {
        setCart(prev => prev.map(item =>
          item.id === productId ? { ...item, quantity } : item
        ));
      }

      // Sync with database
      if (user.id) {
        if (quantity === 0) {
          await liveDataService.removeCartItem(user.id, productId);
        } else {
          await liveDataService.updateCartItem(user.id, productId, quantity);
        }
      }
    } catch (error) {
      console.error('Error updating cart quantity:', error);
    }
  };

  const removeFromCart = async (productId: number) => {
    try {
      // Update local state immediately
      setCart(prev => prev.filter(item => item.id !== productId));

      // Sync with database
      if (user.id) {
        await liveDataService.removeCartItem(user.id, productId);
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
    }
  };

  const clearCart = () => {
    setCart([]);
  };

  const cartTotal = cart.reduce((sum, item) => {
    const price = user.userType === 'reseller' ? item.resellerPrice : item.price;
    return sum + (price * item.quantity);
  }, 0);

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string) => {
    setActiveTab(page);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('tab', page);

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('client_active_tab', page);
  };

  // Provide navigation function to header
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }
  }, [onNavigateFromHeader]);

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-white">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2">
              Welcome back, {user.fullName}!
            </h2>
            <p className="text-lg opacity-90">
              {user.userType === 'reseller' ? 'Reseller' : 'Client'} Account
              {user.userType === 'reseller' && (
                <span className="ml-2 bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                  Special Pricing Available
                </span>
              )}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Notification Bell */}
            <NotificationBell userId={user.id} />
            
            <div className="text-right">
              <p className="text-sm opacity-80">Currency</p>
              <p className="text-2xl font-bold">MAD (Dh)</p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'products', label: 'Products', icon: Package },
          { id: 'wishlist', label: 'Wishlist', icon: Heart },
          { id: 'orders', label: 'My Orders', icon: ShoppingCart },
          { id: 'security', label: 'Security', icon: Shield },
          { id: 'profile', label: 'Profile Settings', icon: User }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Best Selling Products Section */}
      <div className="bg-gradient-to-br from-teal-50 via-white to-orange-50 rounded-xl shadow-lg border border-teal-100 p-6 relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-transparent rounded-full opacity-30 -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-orange-100 to-transparent rounded-full opacity-40 translate-y-12 -translate-x-12"></div>

        <BestSellingProducts
          userType={user.userType as 'client' | 'reseller'}
          customerId={user.id}
          customerName={user.fullName}
          onAddToCart={addToCart}
          onViewProduct={(productId) => console.log('View product:', productId)}
          onAddToWishlist={(product) => console.log('Add to wishlist:', product)}
        />
      </div>

      {/* Products Tab */}
      {activeTab === 'products' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Quick Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Quick search products..."
                  value={searchFilters.searchTerm}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              {/* Quick Category Filter */}
              <div className="lg:w-64">
                <select
                  value={searchFilters.category}
                  onChange={(e) => setSearchFilters(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Advanced Search Button */}
              <button
                onClick={() => setShowAdvancedSearch(true)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
              >
                <Filter className="h-5 w-5" />
                <span>Advanced</span>
              </button>

              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 ${viewMode === 'grid' ? 'bg-teal-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-3 ${viewMode === 'list' ? 'bg-teal-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
                >
                  <List className="h-5 w-5" />
                </button>
              </div>

              {/* Cart Button */}
              <button
                onClick={() => setShowCart(true)}
                className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 font-semibold flex items-center space-x-2"
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Cart ({cart.length})</span>
                {cartTotal > 0 && (
                  <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-sm">
                    {cartTotal.toFixed(2)} Dh
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Recently Viewed */}
          <RecentlyViewed
            userType={user.userType}
            onAddToCart={addToCart}
          />

          {/* Products Grid/List */}
          <ProductGrid
            viewMode={viewMode}
            searchFilters={searchFilters}
            userType={user.userType}
            customerId={user.id}
            onAddToCart={addToCart}
            onAddToWishlist={(product) => console.log('Add to wishlist:', product)}
          />
        </div>
      )}

      {/* Wishlist Tab */}
      {activeTab === 'wishlist' && (
        <WishlistDashboard
          customerId={user.id}
          userType={user.userType as 'client' | 'reseller'}
          onAddToCart={addToCart}
        />
      )}

      {/* Orders Tab */}
      {activeTab === 'orders' && (
        <OrderHistory customerId="current-user-id" />
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <SecurityDashboard userRole={user.userType} />
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
      )}

      {/* Modals */}
      {showCart && (
        <Cart
          isOpen={showCart}
          items={cart}
          userType={user.userType}
          onClose={() => setShowCart(false)}
          onUpdateQuantity={updateCartQuantity}
          onRemoveItem={removeFromCart}
          onClearCart={clearCart}
        />
      )}

      {showAdvancedSearch && (
        <AdvancedSearch
          filters={searchFilters}
          onFiltersChange={setSearchFilters}
          categories={categories}
          brands={brands}
          priceRange={[0, 1000]}
          isOpen={showAdvancedSearch}
          onClose={() => setShowAdvancedSearch(false)}
        />
      )}
    </div>
  );
};

export default ClientDashboard;
