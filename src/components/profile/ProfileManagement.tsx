
import { useState, useEffect } from 'react';
import { User, Mail, Phone, MapPin, Building, Save, FileText, CreditCard, Shield } from 'lucide-react';
import { updateUser } from '../../services/userManagementService';
import { MOROCCAN_CITIES } from '../../constants/cities';
import { supabase } from '../../integrations/supabase/client';

interface ProfileManagementProps {
  user: any;
  onUserUpdate: (updatedUser: any) => void;
}

const ProfileManagement = ({ user, onUserUpdate }: ProfileManagementProps) => {
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    // Map from user object (snake_case) to form fields (camelCase)
    fullName: user.full_name || user.fullName || '',
    email: user.email || '',
    phone: user.phone || '',
    city: user.city || '',
    company: user.company || '',
    jobTitle: user.job_title || user.jobTitle || '',
    bio: user.bio || '',
    // Company information
    isCompany: user.is_company || user.isCompany || false,
    companyName: user.company_name || user.companyName || '',
    iceNumber: user.ice_number || user.iceNumber || '',
    companyAddress: user.company_address || user.companyAddress || '',
    companyPhone: user.company_phone || user.companyPhone || '',
    companyCity: user.company_city || user.companyCity || '',
    companyEmail: user.company_email || user.companyEmail || '',
    taxId: user.tax_id || user.taxId || '',
    legalForm: user.legal_form || user.legalForm || ''
  });



  // Role-based visibility check
  const isAdminOrManager = user.userType === 'admin' || user.userType === 'manager';
  const shouldHideAccountType = isAdminOrManager;

  // Update form data when user prop changes (for data persistence)
  useEffect(() => {
    console.log('ProfileManagement: User prop changed, updating form data:', user);
    setFormData({
      // Map from user object (snake_case) to form fields (camelCase)
      fullName: user.full_name || user.fullName || '',
      email: user.email || '',
      phone: user.phone || '',
      city: user.city || '',
      company: user.company || '',
      jobTitle: user.job_title || user.jobTitle || '',
      bio: user.bio || '',
      // Company information
      isCompany: user.is_company || user.isCompany || false,
      companyName: user.company_name || user.companyName || '',
      iceNumber: user.ice_number || user.iceNumber || '',
      companyAddress: user.company_address || user.companyAddress || '',
      companyPhone: user.company_phone || user.companyPhone || '',
      companyCity: user.company_city || user.companyCity || '',
      companyEmail: user.company_email || user.companyEmail || '',
      taxId: user.tax_id || user.taxId || '',
      legalForm: user.legal_form || user.legalForm || ''
    });
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('ProfileManagement: Updating profile with form data:', formData);

      // Map form data to database field names (camelCase to snake_case)
      const mappedUserData = {
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        city: formData.city,
        company: formData.company,
        job_title: formData.jobTitle,
        bio: formData.bio,
        is_company: formData.isCompany,
        company_name: formData.companyName,
        ice_number: formData.iceNumber,
        company_address: formData.companyAddress,
        company_phone: formData.companyPhone,
        company_city: formData.companyCity,
        company_email: formData.companyEmail,
        tax_id: formData.taxId,
        legal_form: formData.legalForm
      };

      console.log('ProfileManagement: Mapped data for database update:', mappedUserData);

      // Update user through the userManagementService to ensure real-time sync
      const updatedUser = await updateUser(user.id, mappedUserData, user.id);

      console.log('ProfileManagement: Update result:', updatedUser);

      if (updatedUser) {
        // Update the local form data to reflect the saved changes
        setFormData({
          fullName: updatedUser.full_name || '',
          email: updatedUser.email || '',
          phone: updatedUser.phone || '',
          city: updatedUser.city || '',
          company: updatedUser.company || '',
          jobTitle: updatedUser.job_title || '',
          bio: updatedUser.bio || '',
          isCompany: updatedUser.is_company || false,
          companyName: updatedUser.company_name || '',
          iceNumber: updatedUser.ice_number || '',
          companyAddress: updatedUser.company_address || '',
          companyPhone: updatedUser.company_phone || '',
          companyCity: updatedUser.company_city || '',
          companyEmail: updatedUser.company_email || '',
          taxId: updatedUser.tax_id || '',
          legalForm: updatedUser.legal_form || ''
        });

        // Notify parent component of the update
        onUserUpdate(updatedUser);

        console.log('ProfileManagement: Profile updated successfully');
        alert('Profile updated successfully!');
      } else {
        throw new Error('Update operation returned null or undefined');
      }
    } catch (error) {
      console.error('ProfileManagement: Error updating profile:', error);
      alert(`Error updating profile: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  const moroccanCities = MOROCCAN_CITIES;

  const legalForms = [
    'SARL', 'SA', 'SAS', 'SNC', 'SCS', 'Auto-entrepreneur', 'Entreprise individuelle'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h3>
        <p className="text-gray-600 dark:text-gray-300">Manage your personal and company information</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Account Type Selection - Hidden for Admin/Manager roles */}
        {!shouldHideAccountType && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Account Type
            </h4>
            <div className="flex space-x-6">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={!formData.isCompany}
                  onChange={() => handleInputChange('isCompany', false)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Individual</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={formData.isCompany}
                  onChange={() => handleInputChange('isCompany', true)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Company</span>
              </label>
            </div>
          </div>
        )}

        {/* Security Notice for Admin/Manager */}
        {shouldHideAccountType && (
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-amber-600 mr-2" />
              <div>
                <h4 className="text-sm font-semibold text-amber-800">Account Type Restriction</h4>
                <p className="text-sm text-amber-700">
                  As an {user.userType}, you cannot modify your account type for security reasons.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Personal Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <User className="h-5 w-5 text-teal-600 mr-2" />
            {formData.isCompany ? 'Contact Person Information' : 'Personal Information'}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Full Name
              </label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Job Title
              </label>
              <input
                type="text"
                value={formData.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Bio
            </label>
            <textarea
              rows={3}
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Brief description about yourself..."
            />
          </div>
        </div>

        {/* Company Information (only shown if isCompany is true) */}
        {formData.isCompany && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Company Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required={formData.isCompany}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ICE Number
                </label>
                <input
                  type="text"
                  value={formData.iceNumber}
                  onChange={(e) => handleInputChange('iceNumber', e.target.value)}
                  placeholder="000000000000000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tax ID
                </label>
                <input
                  type="text"
                  value={formData.taxId}
                  onChange={(e) => handleInputChange('taxId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Legal Form
                </label>
                <select
                  value={formData.legalForm}
                  onChange={(e) => handleInputChange('legalForm', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select legal form</option>
                  {legalForms.map(form => (
                    <option key={form} value={form}>{form}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Address
                </label>
                <textarea
                  rows={2}
                  value={formData.companyAddress}
                  onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Company address..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 text-teal-600 mr-2" />
            Contact Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+212 6 XX XX XX XX"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City
              </label>
              <select
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select city</option>
                {moroccanCities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
            {!formData.isCompany && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company (Optional)
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            )}
            {formData.isCompany && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.companyPhone}
                    onChange={(e) => handleInputChange('companyPhone', e.target.value)}
                    placeholder="+212 5 XX XX XX XX"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Email
                  </label>
                  <input
                    type="email"
                    value={formData.companyEmail}
                    onChange={(e) => handleInputChange('companyEmail', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company City
                  </label>
                  <select
                    value={formData.companyCity}
                    onChange={(e) => handleInputChange('companyCity', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select company city</option>
                    {moroccanCities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="bg-teal-600 text-white py-2 px-6 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{loading ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </form>




    </div>
  );
};

export default ProfileManagement;
