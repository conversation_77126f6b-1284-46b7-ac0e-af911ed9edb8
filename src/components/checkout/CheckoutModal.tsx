
import { useState, useEffect } from 'react';
import { X, MapPin, Truck, Tag, Building2 } from 'lucide-react';
import { PromoCode } from '../../types/promoCode';
import { MOROCCAN_CITIES } from '../../constants/cities';
import DeliveryLocationMap from '../map/DeliveryLocationMap';
import { getBranches } from '../../services/branchService';
import { Branch } from '../../types/branch';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  cartItems: any[];
  subtotal: number;
  deliveryFee: number;
  discountAmount: number;
  appliedPromo: PromoCode | null;
  onCheckout: (orderData: any) => void;
}

const CheckoutModal = ({ 
  isOpen, 
  onClose, 
  cartItems, 
  subtotal, 
  deliveryFee, 
  discountAmount, 
  appliedPromo, 
  onCheckout 
}: CheckoutModalProps) => {
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    postalCode: '',
    latitude: null as number | null,
    longitude: null as number | null,
    preferredBranchId: '',
    paymentMethod: 'cash',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(true);
  const total = subtotal + deliveryFee - discountAmount;

  // Load active branches on component mount
  useEffect(() => {
    const loadBranches = async () => {
      try {
        setLoadingBranches(true);
        const activeBranches = await getBranches(true); // Get only active branches
        setBranches(activeBranches);
      } catch (error) {
        console.error('Error loading branches:', error);
        setBranches([]);
      } finally {
        setLoadingBranches(false);
      }
    };

    if (isOpen) {
      loadBranches();
    }
  }, [isOpen]);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in kilometers
    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  };

  // Get branch display text with distance if available
  const getBranchDisplayText = (branch: Branch): string => {
    let displayText = `${branch.name} - ${branch.address.city}`;

    // Add distance if both user location and branch coordinates are available
    if (formData.latitude && formData.longitude) {
      // Try to get coordinates from the branch data
      // The coordinates might be in different places depending on the branch type/source
      let branchLat: number | null = null;
      let branchLng: number | null = null;

      // Check various possible locations for coordinates
      if ((branch as any).coordinates) {
        branchLat = (branch as any).coordinates.latitude;
        branchLng = (branch as any).coordinates.longitude;
      }

      // For now, we'll use mock coordinates for major Moroccan cities
      // In a real implementation, these would be stored in the database
      const cityCoordinates: { [key: string]: { lat: number; lng: number } } = {
        'Casablanca': { lat: 33.5731, lng: -7.5898 },
        'Rabat': { lat: 34.0209, lng: -6.8416 },
        'Fes': { lat: 34.0181, lng: -5.0078 },
        'Marrakech': { lat: 31.6295, lng: -7.9811 },
        'Tangier': { lat: 35.7595, lng: -5.8340 },
        'Agadir': { lat: 30.4278, lng: -9.5981 },
        'Meknes': { lat: 33.8935, lng: -5.5473 },
        'Oujda': { lat: 34.6814, lng: -1.9086 }
      };

      // Use city coordinates as fallback
      if (!branchLat || !branchLng) {
        const cityCoords = cityCoordinates[branch.address.city];
        if (cityCoords) {
          branchLat = cityCoords.lat;
          branchLng = cityCoords.lng;
        }
      }

      if (branchLat && branchLng) {
        const distance = calculateDistance(
          formData.latitude,
          formData.longitude,
          branchLat,
          branchLng
        );
        displayText += ` (Distance: ${distance} km)`;
      }
    }

    return displayText;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    // Validate required fields including branch selection
    if (!formData.preferredBranchId) {
      alert('Please select a preferred delivery branch.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Find selected branch details
      const selectedBranch = branches.find(branch => branch.id === formData.preferredBranchId);

      const orderData = {
        ...formData,
        items: cartItems,
        subtotal,
        deliveryFee,
        discountAmount,
        total,
        promoCode: appliedPromo?.code || undefined,
        orderDate: new Date().toISOString(),
        deliveryCoordinates: formData.latitude && formData.longitude ? {
          latitude: formData.latitude,
          longitude: formData.longitude
        } : null,
        // Include branch information
        preferredBranchId: formData.preferredBranchId,
        preferredBranchName: selectedBranch?.name || '',
        preferredBranchCity: selectedBranch?.address?.city || '',
        preferredBranchAddress: selectedBranch?.address ?
          `${selectedBranch.address.street}, ${selectedBranch.address.city}` : ''
      };

      await onCheckout(orderData);
      onClose();
    } catch (error) {
      console.error('Checkout error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLocationSelect = (lat: number, lng: number, address?: string) => {
    setFormData(prev => ({
      ...prev,
      latitude: lat,
      longitude: lng,
      // Auto-populate street address if provided and current address is empty or just coordinates
      address: address && (!prev.address || prev.address.includes(',') || prev.address.trim() === '')
        ? address
        : prev.address
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Checkout</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Delivery Address */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              Delivery Address
            </h3>
            <div className="space-y-4">
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">
                  Street Address
                </label>
                <input
                  type="text"
                  placeholder="Enter your street address or select location on map"
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                />
                {formData.latitude && formData.longitude && (
                  <p className="text-xs text-teal-600 flex items-center space-x-1">
                    <MapPin className="h-3 w-3" />
                    <span>Address auto-filled from map location. You can edit it above.</span>
                  </p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <select
                  value={formData.city}
                  onChange={(e) => setFormData({...formData, city: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                >
                  <option value="">Select City</option>
                  {MOROCCAN_CITIES.map(city => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
                <input
                  type="text"
                  placeholder="Postal Code"
                  value={formData.postalCode}
                  onChange={(e) => setFormData({...formData, postalCode: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                />
              </div>

              {/* Preferred Delivery Branch Selection */}
              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700 flex items-center">
                  <Building2 className="h-4 w-4 mr-1" />
                  Select Nearest Branch for Delivery
                </label>
                <select
                  value={formData.preferredBranchId}
                  onChange={(e) => setFormData({...formData, preferredBranchId: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                  required
                  disabled={loadingBranches}
                >
                  <option value="">
                    {loadingBranches ? 'Loading branches...' : 'Choose your preferred branch...'}
                  </option>
                  {branches.map(branch => (
                    <option key={branch.id} value={branch.id}>
                      {getBranchDisplayText(branch)}
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500">
                  This helps our delivery team route your order efficiently
                </p>
                {formData.preferredBranchId && (
                  <p className="text-xs text-teal-600 flex items-center space-x-1">
                    <Building2 className="h-3 w-3" />
                    <span>
                      Branch selected: {branches.find(b => b.id === formData.preferredBranchId)?.name}
                    </span>
                  </p>
                )}
              </div>

              {/* Interactive Map Component */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Pin your exact delivery location on the map
                </label>
                <DeliveryLocationMap
                  onLocationSelect={handleLocationSelect}
                  selectedCity={formData.city}
                  initialPosition={
                    formData.latitude && formData.longitude
                      ? { lat: formData.latitude, lng: formData.longitude }
                      : undefined
                  }
                />
              </div>
            </div>
          </div>

          {/* Applied Promo Code Display */}
          {appliedPromo && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                Applied Promo Code
              </h3>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-green-800 font-medium">Code: {appliedPromo.code}</p>
                    <p className="text-green-600 text-sm">{appliedPromo.description}</p>
                    <p className="text-green-600 text-sm">Discount: -{discountAmount.toFixed(2)} DH</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Payment Method */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Payment Method
            </h3>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="cash"
                  checked={formData.paymentMethod === 'cash'}
                  onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                  className="text-teal-600"
                  readOnly
                />
                <div>
                  <span className="font-medium text-gray-900">Cash on Delivery</span>
                  <p className="text-sm text-gray-600">Pay when your order is delivered</p>
                </div>
              </label>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{subtotal.toFixed(2)} DH</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee</span>
                <span>{deliveryFee.toFixed(2)} DH</span>
              </div>
              {discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount</span>
                  <span>-{discountAmount.toFixed(2)} DH</span>
                </div>
              )}
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>Total</span>
                <span>{total.toFixed(2)} DH</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Notes (Optional)</h3>
            <textarea
              placeholder="Special instructions or notes for your order"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 resize-none"
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-teal-600 text-white py-3 px-6 rounded-lg hover:bg-teal-700 transition-colors flex items-center justify-center space-x-2 disabled:bg-gray-400"
          >
            <Truck className="h-5 w-5" />
            <span>
              {isSubmitting ? 'Processing...' : `Place Order - ${total.toFixed(2)} DH`}
            </span>
          </button>
        </form>
      </div>
    </div>
  );
};

export default CheckoutModal;
