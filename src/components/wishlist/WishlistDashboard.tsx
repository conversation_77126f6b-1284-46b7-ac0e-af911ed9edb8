import { useState } from 'react';
import { Heart, ShoppingCart, Eye, Trash2, Package, Star, Filter, Grid, List } from 'lucide-react';
import { useWishlist } from '../../hooks/useWishlist';
import { formatPrice } from '../../utils/inventoryUtils';
import ProductDetailsModal from '../products/ProductDetailsModal';
import YalaPagination from '../ui/YalaPagination';

interface WishlistDashboardProps {
  customerId: string;
  userType: 'client' | 'reseller';
  onAddToCart: (product: any) => void;
}

const WishlistDashboard = ({ customerId, userType, onAddToCart }: WishlistDashboardProps) => {
  const {
    wishlistItems,
    wishlistCount,
    loading,
    error,
    removeFromWishlist,
    moveToCart,
    clearWishlist
  } = useWishlist(customerId);

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'price-low' | 'price-high' | 'name'>('newest');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 20;

  // Get unique categories for filter
  const categories = ['all', ...new Set(wishlistItems.map(item => item.category))];

  // Filter and sort wishlist items
  const filteredAndSortedItems = wishlistItems
    .filter(item => filterCategory === 'all' || item.category === filterCategory)
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
        case 'oldest':
          return new Date(a.addedAt).getTime() - new Date(b.addedAt).getTime();
        case 'price-low':
          const priceA = userType === 'reseller' ? a.resellerPrice : a.price;
          const priceB = userType === 'reseller' ? b.resellerPrice : b.price;
          return priceA - priceB;
        case 'price-high':
          const priceA2 = userType === 'reseller' ? a.resellerPrice : a.price;
          const priceB2 = userType === 'reseller' ? b.resellerPrice : b.price;
          return priceB2 - priceA2;
        case 'name':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

  // Pagination calculations
  const totalPages = Math.ceil(filteredAndSortedItems.length / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const paginatedItems = filteredAndSortedItems.slice(startIndex, endIndex);

  const handleRemoveFromWishlist = async (productId: string) => {
    try {
      await removeFromWishlist(productId);
      // Show success feedback
      console.log('Item removed from wishlist successfully');
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      alert('Failed to remove item from wishlist. Please try again.');
    }
  };

  const handleMoveToCart = async (productId: string) => {
    try {
      await moveToCart(productId, onAddToCart);
      // Show success feedback
      console.log('Item moved to cart successfully');
    } catch (error) {
      console.error('Error moving to cart:', error);
      alert('Failed to move item to cart. Please try again.');
    }
  };

  const handleClearWishlist = async () => {
    if (window.confirm('Are you sure you want to clear your entire wishlist?')) {
      try {
        await clearWishlist();
        // Show success feedback
        console.log('Wishlist cleared successfully');
      } catch (error) {
        console.error('Error clearing wishlist:', error);
        alert('Failed to clear wishlist. Please try again.');
      }
    }
  };

  const handleViewProduct = (productId: string) => {
    setSelectedProductId(productId);
    setShowProductModal(true);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <Heart className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Wishlist</h3>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-red-100 to-pink-100 p-3 rounded-xl">
            <Heart className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">My Wishlist</h2>
            <p className="text-sm text-gray-600">
              {wishlistCount} {wishlistCount === 1 ? 'item' : 'items'} saved
            </p>
          </div>
        </div>

        {wishlistItems.length > 0 && (
          <div className="flex items-center space-x-3">
            <button
              onClick={handleClearWishlist}
              className="text-red-600 hover:text-red-700 text-sm font-medium"
            >
              Clear All
            </button>
            
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {wishlistItems.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Your wishlist is empty</h3>
          <p className="text-gray-500 mb-6">
            Start adding products you love to keep track of them
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-gradient-to-r from-teal-500 to-teal-600 text-white px-6 py-3 rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all"
          >
            Browse Products
          </button>
        </div>
      ) : (
        <>
          {/* Filters and Sort */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="name">Name A-Z</option>
              </select>
            </div>
          </div>

          {/* Wishlist Items */}
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
          }>
            {paginatedItems.map(item => {
              const displayPrice = userType === 'reseller' ? item.resellerPrice : item.price;
              const savings = userType === 'reseller' ? item.price - item.resellerPrice : 0;

              return (
                <div
                  key={item.id}
                  className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
                    viewMode === 'list' ? 'flex' : ''
                  }`}
                >
                  {/* Product Image */}
                  <div className={`bg-gray-100 flex items-center justify-center ${
                    viewMode === 'list' ? 'w-32 h-32' : 'h-48'
                  }`}>
                    {item.image && item.image !== '/placeholder.svg' ? (
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Package className="h-12 w-12 text-gray-400" />
                    )}
                  </div>

                  {/* Product Info */}
                  <div className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold text-gray-900 line-clamp-2">
                          {item.title}
                        </h3>
                        <p className="text-sm text-gray-500">{item.category}</p>
                      </div>

                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600">{item.rating.toFixed(1)}</span>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-bold text-gray-900">
                            {formatPrice(displayPrice)}
                          </span>
                          {userType === 'reseller' && savings > 0 && (
                            <span className="text-sm text-gray-500 line-through">
                              {formatPrice(item.price)}
                            </span>
                          )}
                        </div>
                        
                        {userType === 'reseller' && savings > 0 && (
                          <div className="text-sm text-green-600 font-medium">
                            Save {formatPrice(savings)}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>Added {new Date(item.addedAt).toLocaleDateString()}</span>
                        <span className={`px-2 py-1 rounded-full ${
                          item.inStock ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}>
                          {item.inStock ? `${item.stock} left` : 'Out of stock'}
                        </span>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleMoveToCart(item.id)}
                          disabled={!item.inStock}
                          className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors flex items-center justify-center space-x-1 ${
                            item.inStock
                              ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white hover:from-teal-600 hover:to-teal-700'
                              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          }`}
                        >
                          <ShoppingCart className="h-4 w-4" />
                          <span>Add to Cart</span>
                        </button>

                        <button
                          onClick={() => handleViewProduct(item.id)}
                          className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <Eye className="h-4 w-4" />
                        </button>

                        <button
                          onClick={() => handleRemoveFromWishlist(item.id)}
                          className="px-3 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination */}
          {filteredAndSortedItems.length > 0 && totalPages > 1 && (
            <div className="mt-8">
              <YalaPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalRecords={filteredAndSortedItems.length}
                recordsPerPage={recordsPerPage}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </>
      )}

      {/* Product Details Modal */}
      <ProductDetailsModal
        productId={selectedProductId}
        isOpen={showProductModal}
        onClose={() => setShowProductModal(false)}
        userType={userType}
        onAddToCart={onAddToCart}
      />
    </div>
  );
};

export default WishlistDashboard;
