import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { Icon, LatLng } from 'leaflet';
import { MapPin, Search, Locate } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';

const defaultIcon = new Icon({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface DeliveryLocationMapProps {
  onLocationSelect: (lat: number, lng: number, address?: string) => void;
  initialPosition?: { lat: number; lng: number };
  selectedCity?: string;
}

// Component to handle map clicks
const LocationMarker: React.FC<{
  position: LatLng | null;
  setPosition: (position: LatLng) => void;
}> = ({ position, setPosition }) => {
  useMapEvents({
    click(e) {
      setPosition(e.latlng);
    },
  });

  return position === null ? null : (
    <Marker position={position} icon={defaultIcon} />
  );
};

const DeliveryLocationMap: React.FC<DeliveryLocationMapProps> = ({
  onLocationSelect,
  initialPosition,
  selectedCity
}) => {
  const [position, setPosition] = useState<LatLng | null>(
    initialPosition ? new LatLng(initialPosition.lat, initialPosition.lng) : null
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>([33.5731, -7.5898]); // Casablanca, Morocco
  const mapRef = useRef<any>(null);

  // Morocco city coordinates (approximate centers)
  const moroccanCityCoordinates: { [key: string]: [number, number] } = {
    'Casablanca': [33.5731, -7.5898],
    'Rabat': [34.0209, -6.8416],
    'Fes': [34.0181, -5.0078],
    'Marrakech': [31.6295, -7.9811],
    'Tangier': [35.7595, -5.8340],
    'Agadir': [30.4278, -9.5981],
    'Meknes': [33.8935, -5.5473],
    'Oujda': [34.6814, -1.9086],
    'Kenitra': [34.2610, -6.5802],
    'Tetouan': [35.5889, -5.3626],
    'Safi': [32.2994, -9.2372],
    'El Jadida': [33.2316, -8.5007],
    'Beni Mellal': [32.3373, -6.3498],
    'Errachidia': [31.9314, -4.4240],
    'Taza': [34.2133, -4.0103],
    'Essaouira': [31.5085, -9.7595],
    'Khouribga': [32.8811, -6.9063],
    'Ouarzazate': [30.9335, -6.9370],
    'Settat': [33.0013, -7.6216],
    'Larache': [35.1932, -6.1563]
  };

  // Update map center when city changes
  useEffect(() => {
    if (selectedCity && moroccanCityCoordinates[selectedCity]) {
      const cityCoords = moroccanCityCoordinates[selectedCity];
      setMapCenter(cityCoords);
      
      // If map is loaded, fly to the city
      if (mapRef.current) {
        mapRef.current.flyTo(cityCoords, 12);
      }
    }
  }, [selectedCity]);

  // Handle position changes
  useEffect(() => {
    if (position) {
      onLocationSelect(position.lat, position.lng);
    }
  }, [position, onLocationSelect]);

  // Get user's current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newPos = new LatLng(latitude, longitude);
          setPosition(newPos);
          setMapCenter([latitude, longitude]);
          
          if (mapRef.current) {
            mapRef.current.flyTo([latitude, longitude], 15);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your current location. Please click on the map to set your delivery location.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  // Simple geocoding using Nominatim (OpenStreetMap)
  const searchLocation = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery + ', Morocco')}&limit=1`
      );
      const data = await response.json();
      
      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        const newPos = new LatLng(parseFloat(lat), parseFloat(lon));
        setPosition(newPos);
        setMapCenter([parseFloat(lat), parseFloat(lon)]);
        
        if (mapRef.current) {
          mapRef.current.flyTo([parseFloat(lat), parseFloat(lon)], 15);
        }
      } else {
        alert('Location not found. Please try a different search term or click on the map.');
      }
    } catch (error) {
      console.error('Search error:', error);
      alert('Error searching for location. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Search for a specific address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && searchLocation()}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
        <button
          onClick={searchLocation}
          disabled={isSearching || !searchQuery.trim()}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
        <button
          onClick={getCurrentLocation}
          className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors flex items-center space-x-2"
          title="Use my current location"
        >
          <Locate className="h-4 w-4" />
          <span className="hidden sm:inline">My Location</span>
        </button>
      </div>

      {/* Map Container */}
      <div className="relative">
        <div className="h-64 sm:h-80 rounded-lg overflow-hidden border border-gray-300">
          <MapContainer
            center={mapCenter}
            zoom={selectedCity ? 12 : 6}
            style={{ height: '100%', width: '100%' }}
            ref={mapRef}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <LocationMarker position={position} setPosition={setPosition} />
          </MapContainer>
        </div>
        
        {/* Instructions overlay */}
        <div className="absolute top-2 left-2 right-2 bg-white bg-opacity-90 rounded-lg p-2 text-xs text-gray-600">
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3 text-teal-600" />
            <span>Click on the map to set your exact delivery location</span>
          </div>
        </div>
      </div>

      {/* Selected Location Info */}
      {position && (
        <div className="bg-teal-50 border border-teal-200 rounded-lg p-3">
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="h-4 w-4 text-teal-600" />
            <span className="text-teal-800">
              <strong>Selected Location:</strong> {position.lat.toFixed(6)}, {position.lng.toFixed(6)}
            </span>
          </div>
          <p className="text-xs text-teal-600 mt-1">
            This location will be used for accurate delivery. You can click elsewhere on the map to change it.
          </p>
        </div>
      )}
    </div>
  );
};

export default DeliveryLocationMap;
