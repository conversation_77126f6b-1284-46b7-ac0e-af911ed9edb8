import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { Icon, LatLng } from 'leaflet';
import { MapPin, Search, Locate, Plus, Minus } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';

const defaultIcon = new Icon({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface DeliveryLocationMapProps {
  onLocationSelect: (lat: number, lng: number, address?: string) => void;
  initialPosition?: { lat: number; lng: number };
  selectedCity?: string;
}

// Component to handle map clicks
const LocationMarker: React.FC<{
  position: LatLng | null;
  setPosition: (position: LatLng) => void;
}> = ({ position, setPosition }) => {
  useMapEvents({
    click(e) {
      setPosition(e.latlng);
    },
  });

  return position === null ? null : (
    <Marker position={position} icon={defaultIcon} />
  );
};

const DeliveryLocationMap: React.FC<DeliveryLocationMapProps> = ({
  onLocationSelect,
  initialPosition,
  selectedCity
}) => {
  const [position, setPosition] = useState<LatLng | null>(
    initialPosition ? new LatLng(initialPosition.lat, initialPosition.lng) : null
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>([31.7917, -7.0926]); // Center of Morocco
  const mapRef = useRef<any>(null);

  // Morocco city coordinates (approximate centers)
  const moroccanCityCoordinates: { [key: string]: [number, number] } = {
    'Casablanca': [33.5731, -7.5898],
    'Rabat': [34.0209, -6.8416],
    'Fes': [34.0181, -5.0078],
    'Marrakech': [31.6295, -7.9811],
    'Tangier': [35.7595, -5.8340],
    'Agadir': [30.4278, -9.5981],
    'Meknes': [33.8935, -5.5473],
    'Oujda': [34.6814, -1.9086],
    'Kenitra': [34.2610, -6.5802],
    'Tetouan': [35.5889, -5.3626],
    'Safi': [32.2994, -9.2372],
    'El Jadida': [33.2316, -8.5007],
    'Beni Mellal': [32.3373, -6.3498],
    'Errachidia': [31.9314, -4.4240],
    'Taza': [34.2133, -4.0103],
    'Essaouira': [31.5085, -9.7595],
    'Khouribga': [32.8811, -6.9063],
    'Ouarzazate': [30.9335, -6.9370],
    'Settat': [33.0013, -7.6216],
    'Larache': [35.1932, -6.1563]
  };

  // Update map center when city changes
  useEffect(() => {
    if (selectedCity && moroccanCityCoordinates[selectedCity]) {
      const cityCoords = moroccanCityCoordinates[selectedCity];
      setMapCenter(cityCoords);
      setZoomLevel(12); // City-level zoom

      // Auto-set position to city center if no position is set
      if (!position) {
        const newPos = new LatLng(cityCoords[0], cityCoords[1]);
        setPosition(newPos);
      }
    }
  }, [selectedCity, position]);

  // Handle position changes - use useRef to avoid infinite loops
  const previousPosition = useRef<{ lat: number; lng: number } | null>(null);

  useEffect(() => {
    if (position && (!previousPosition.current ||
        previousPosition.current.lat !== position.lat ||
        previousPosition.current.lng !== position.lng)) {
      previousPosition.current = position;
      onLocationSelect(position.lat, position.lng);
    }
  }, [position]);

  // Get user's current location with improved error handling
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser. Please search for your address or click on the map to set your delivery location.');
      return;
    }

    // Show loading state
    setIsGettingLocation(true);

    const options = {
      enableHighAccuracy: true,
      timeout: 10000, // 10 seconds timeout
      maximumAge: 300000 // 5 minutes cache
    };

    navigator.geolocation.getCurrentPosition(
      (geoPosition) => {
        const { latitude, longitude, accuracy } = geoPosition.coords;
        const newPos = new LatLng(latitude, longitude);
        setPosition(newPos);
        setMapCenter([latitude, longitude]);

        if (mapRef.current) {
          mapRef.current.flyTo([latitude, longitude], 15);
        }

        // Show success message with accuracy info
        const accuracyText = accuracy < 100 ? 'high accuracy' : accuracy < 1000 ? 'medium accuracy' : 'low accuracy';
        console.log(`Location found with ${accuracyText} (±${Math.round(accuracy)}m)`);

        // Reset loading state
        setIsGettingLocation(false);
      },
      (error) => {
        console.error('Geolocation error:', error);

        let errorMessage = 'Unable to get your current location. ';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage += 'Location access was denied. Please enable location permissions in your browser settings and try again, or search for your address manually.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage += 'Location information is unavailable. Please check your internet connection or search for your address manually.';
            break;
          case error.TIMEOUT:
            errorMessage += 'Location request timed out. Please try again or search for your address manually.';
            break;
          default:
            errorMessage += 'An unknown error occurred. Please search for your address or click on the map to set your delivery location.';
            break;
        }

        alert(errorMessage);

        // Reset loading state
        setIsGettingLocation(false);
      },
      options
    );
  };

  // Autocomplete search using Nominatim
  const searchSuggestions = async (query: string) => {
    if (!query.trim() || query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query + ', Morocco')}&limit=5&addressdetails=1`
      );
      const data = await response.json();

      setSuggestions(data || []);
      setShowSuggestions(true);
    } catch (error) {
      console.error('Autocomplete error:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);

    // Debounce the search
    const timeoutId = setTimeout(() => {
      searchSuggestions(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // Select a suggestion
  const selectSuggestion = (suggestion: any) => {
    const { lat, lon, display_name } = suggestion;
    const newPos = new LatLng(parseFloat(lat), parseFloat(lon));
    setPosition(newPos);
    setMapCenter([parseFloat(lat), parseFloat(lon)]);
    setSearchQuery(display_name);
    setShowSuggestions(false);

    if (mapRef.current) {
      mapRef.current.flyTo([parseFloat(lat), parseFloat(lon)], 15);
    }
  };

  // Simple geocoding using Nominatim (OpenStreetMap)
  const searchLocation = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setShowSuggestions(false);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery + ', Morocco')}&limit=1`
      );
      const data = await response.json();

      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        const newPos = new LatLng(parseFloat(lat), parseFloat(lon));
        setPosition(newPos);
        setMapCenter([parseFloat(lat), parseFloat(lon)]);

        if (mapRef.current) {
          mapRef.current.flyTo([parseFloat(lat), parseFloat(lon)], 15);
        }
      } else {
        alert('Location not found. Please try a different search term or use the city selector.');
      }
    } catch (error) {
      console.error('Search error:', error);
      alert('Error searching for location. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };



  return (
    <div className="space-y-4">
      {/* Location Help Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
        <div className="flex items-start space-x-2">
          <div className="text-blue-600 mt-0.5">💡</div>
          <div className="text-blue-800">
            <strong>How to set your delivery location:</strong>
            <ul className="mt-1 space-y-1 text-xs">
              <li>• <strong>Search:</strong> Type your address in the search box below</li>
              <li>• <strong>GPS:</strong> Click "My Location" to use your current position (requires location permission)</li>
              <li>• <strong>Map:</strong> Click anywhere on the map to place your delivery marker</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Search for a specific address in Morocco..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && searchLocation()}
            onFocus={() => searchQuery.length >= 3 && setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />

          {/* Autocomplete Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => selectSuggestion(suggestion)}
                  className="w-full text-left px-4 py-2 hover:bg-teal-50 border-b border-gray-100 last:border-b-0 text-sm"
                >
                  <div className="font-medium text-gray-900">{suggestion.display_name}</div>
                  {suggestion.address && (
                    <div className="text-xs text-gray-500">
                      {suggestion.address.city || suggestion.address.town || suggestion.address.village}, Morocco
                    </div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>
        <button
          onClick={searchLocation}
          disabled={isSearching || !searchQuery.trim()}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
        <button
          onClick={getCurrentLocation}
          disabled={isGettingLocation}
          className={`px-4 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
            isGettingLocation
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-amber-500 text-white hover:bg-amber-600'
          }`}
          title={isGettingLocation ? "Getting your location..." : "Use my current location"}
        >
          {isGettingLocation ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              <span className="hidden sm:inline">Getting Location...</span>
            </>
          ) : (
            <>
              <Locate className="h-4 w-4" />
              <span className="hidden sm:inline">My Location</span>
            </>
          )}
        </button>
      </div>

      {/* Real Interactive Map */}
      <div className="relative">
        <div className="h-64 sm:h-80 rounded-lg overflow-hidden border border-gray-300">
          <MapContainer
            center={mapCenter}
            zoom={selectedCity ? 12 : 6}
            style={{ height: '100%', width: '100%' }}
            ref={mapRef}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <LocationMarker position={position} setPosition={setPosition} />
          </MapContainer>
        </div>

        {/* City Indicator */}
        {selectedCity && (
          <div className="absolute top-2 left-2 bg-teal-600 text-white px-2 py-1 rounded text-xs font-medium z-[1000]">
            📍 {selectedCity}
          </div>
        )}

        {/* Instructions overlay */}
        <div className="absolute bottom-2 left-2 right-2 bg-white bg-opacity-90 rounded-lg p-2 text-xs text-gray-600 z-[1000]">
          <div className="flex items-center space-x-1">
            <MapPin className="h-3 w-3 text-teal-600" />
            <span>Click on the map to set your exact delivery location</span>
          </div>
        </div>
      </div>

      {/* Selected Location Info */}
      {position && (
        <div className="bg-teal-50 border border-teal-200 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm">
              <MapPin className="h-4 w-4 text-teal-600" />
              <span className="text-teal-800">
                <strong>Delivery Location Set</strong>
              </span>
            </div>
            <div className="text-xs text-teal-600 font-mono">
              {position.lat.toFixed(6)}, {position.lng.toFixed(6)}
            </div>
          </div>
          <p className="text-xs text-teal-600 mt-1">
            📍 Precise coordinates saved for accurate delivery. Use zoom controls to fine-tune location or drag to explore nearby areas.
          </p>
        </div>
      )}

      {!position && (
        <div className="space-y-3">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-sm">
              <MapPin className="h-4 w-4 text-amber-600" />
              <span className="text-amber-800">
                <strong>No location selected yet</strong>
              </span>
            </div>
            <p className="text-xs text-amber-600 mt-1">
              Please search for an address, use your current location, or click on the map above to set your delivery location.
            </p>
          </div>

          {/* Troubleshooting Section */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <div className="text-sm text-gray-700">
              <strong>Having trouble with location access?</strong>
              <div className="mt-2 space-y-2 text-xs">
                <div>
                  <strong>For Chrome/Edge:</strong> Click the location icon 🌐 in the address bar and select "Allow"
                </div>
                <div>
                  <strong>For Firefox:</strong> Click the shield icon and enable location sharing
                </div>
                <div>
                  <strong>For Safari:</strong> Go to Safari → Settings → Websites → Location and allow access
                </div>
                <div className="pt-1 border-t border-gray-300">
                  <strong>Alternative:</strong> Simply search for your city or address, or click directly on the map to place your delivery marker.
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryLocationMap;
