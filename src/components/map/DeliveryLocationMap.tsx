import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Search, Locate } from 'lucide-react';

interface DeliveryLocationMapProps {
  onLocationSelect: (lat: number, lng: number, address?: string) => void;
  initialPosition?: { lat: number; lng: number };
  selectedCity?: string;
}

const DeliveryLocationMap: React.FC<DeliveryLocationMapProps> = ({
  onLocationSelect,
  initialPosition,
  selectedCity
}) => {
  const [position, setPosition] = useState<{ lat: number; lng: number } | null>(
    initialPosition || null
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>([33.5731, -7.5898]); // Casablanca, Morocco
  const mapRef = useRef<HTMLDivElement>(null);

  // Morocco city coordinates (approximate centers)
  const moroccanCityCoordinates: { [key: string]: [number, number] } = {
    'Casablanca': [33.5731, -7.5898],
    'Rabat': [34.0209, -6.8416],
    'Fes': [34.0181, -5.0078],
    'Marrakech': [31.6295, -7.9811],
    'Tangier': [35.7595, -5.8340],
    'Agadir': [30.4278, -9.5981],
    'Meknes': [33.8935, -5.5473],
    'Oujda': [34.6814, -1.9086],
    'Kenitra': [34.2610, -6.5802],
    'Tetouan': [35.5889, -5.3626],
    'Safi': [32.2994, -9.2372],
    'El Jadida': [33.2316, -8.5007],
    'Beni Mellal': [32.3373, -6.3498],
    'Errachidia': [31.9314, -4.4240],
    'Taza': [34.2133, -4.0103],
    'Essaouira': [31.5085, -9.7595],
    'Khouribga': [32.8811, -6.9063],
    'Ouarzazate': [30.9335, -6.9370],
    'Settat': [33.0013, -7.6216],
    'Larache': [35.1932, -6.1563]
  };

  // Update map center when city changes
  useEffect(() => {
    if (selectedCity && moroccanCityCoordinates[selectedCity]) {
      const cityCoords = moroccanCityCoordinates[selectedCity];
      setMapCenter(cityCoords);

      // Auto-set position to city center if no position is set
      if (!position) {
        const newPos = { lat: cityCoords[0], lng: cityCoords[1] };
        setPosition(newPos);
      }
    }
  }, [selectedCity, position]);

  // Handle position changes
  useEffect(() => {
    if (position) {
      onLocationSelect(position.lat, position.lng);
    }
  }, [position, onLocationSelect]);

  // Get user's current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (geoPosition) => {
          const { latitude, longitude } = geoPosition.coords;
          const newPos = { lat: latitude, lng: longitude };
          setPosition(newPos);
          setMapCenter([latitude, longitude]);
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your current location. Please click on the map to set your delivery location.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  // Simple geocoding using Nominatim (OpenStreetMap)
  const searchLocation = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery + ', Morocco')}&limit=1`
      );
      const data = await response.json();

      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        const newPos = { lat: parseFloat(lat), lng: parseFloat(lon) };
        setPosition(newPos);
        setMapCenter([parseFloat(lat), parseFloat(lon)]);
      } else {
        alert('Location not found. Please try a different search term or use the city selector.');
      }
    } catch (error) {
      console.error('Search error:', error);
      alert('Error searching for location. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  // Handle map click simulation
  const handleMapClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Convert click position to approximate coordinates
    // This is a simplified calculation for demonstration
    const mapWidth = rect.width;
    const mapHeight = rect.height;

    // Use current map center as base and adjust based on click position
    const latOffset = ((y - mapHeight / 2) / mapHeight) * -0.01; // Negative because y increases downward
    const lngOffset = ((x - mapWidth / 2) / mapWidth) * 0.01;

    const newPos = {
      lat: mapCenter[0] + latOffset,
      lng: mapCenter[1] + lngOffset
    };

    setPosition(newPos);
  };

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Search for a specific address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && searchLocation()}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          />
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
        </div>
        <button
          onClick={searchLocation}
          disabled={isSearching || !searchQuery.trim()}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isSearching ? 'Searching...' : 'Search'}
        </button>
        <button
          onClick={getCurrentLocation}
          className="px-4 py-2 bg-amber-500 text-white rounded-lg hover:bg-amber-600 transition-colors flex items-center space-x-2"
          title="Use my current location"
        >
          <Locate className="h-4 w-4" />
          <span className="hidden sm:inline">My Location</span>
        </button>
      </div>

      {/* Interactive Map Interface */}
      <div className="relative">
        <div
          className="h-64 sm:h-80 rounded-lg overflow-hidden border-2 border-dashed border-teal-300 bg-gradient-to-br from-teal-50 via-blue-50 to-green-50 cursor-crosshair relative hover:border-teal-400 transition-colors"
          onClick={handleMapClick}
          ref={mapRef}
        >
          {/* Map Background Pattern */}
          <div className="absolute inset-0 opacity-30">
            <div className="grid grid-cols-12 grid-rows-8 h-full w-full">
              {Array.from({ length: 96 }).map((_, i) => (
                <div key={i} className="border border-teal-200 border-opacity-40"></div>
              ))}
            </div>
          </div>

          {/* Map Icons for Visual Appeal */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-4 left-8 text-teal-400 opacity-60">🏢</div>
            <div className="absolute top-12 right-12 text-green-400 opacity-60">🌳</div>
            <div className="absolute bottom-16 left-16 text-blue-400 opacity-60">🏠</div>
            <div className="absolute bottom-8 right-8 text-amber-400 opacity-60">🏪</div>
            <div className="absolute top-1/2 left-1/4 text-gray-400 opacity-40">🛣️</div>
            <div className="absolute top-1/3 right-1/3 text-purple-400 opacity-50">🏛️</div>
          </div>

          {/* City Indicator */}
          {selectedCity && (
            <div className="absolute top-4 left-4 bg-teal-600 text-white px-2 py-1 rounded text-xs font-medium">
              📍 {selectedCity}
            </div>
          )}

          {/* Position Marker */}
          {position && (
            <div
              className="absolute transform -translate-x-1/2 -translate-y-full"
              style={{
                left: '50%',
                top: '50%'
              }}
            >
              <div className="bg-red-500 text-white p-2 rounded-full shadow-lg animate-pulse">
                <MapPin className="h-4 w-4" />
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-500"></div>
            </div>
          )}

          {/* Instructions overlay */}
          <div className="absolute bottom-2 left-2 right-2 bg-white bg-opacity-90 rounded-lg p-2 text-xs text-gray-600">
            <div className="flex items-center space-x-1">
              <MapPin className="h-3 w-3 text-teal-600" />
              <span>Click anywhere on this area to set your delivery location</span>
            </div>
          </div>
        </div>
      </div>

      {/* Selected Location Info */}
      {position && (
        <div className="bg-teal-50 border border-teal-200 rounded-lg p-3">
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="h-4 w-4 text-teal-600" />
            <span className="text-teal-800">
              <strong>Delivery Location Set:</strong> {position.lat.toFixed(6)}, {position.lng.toFixed(6)}
            </span>
          </div>
          <p className="text-xs text-teal-600 mt-1">
            This location will be used for accurate delivery. You can search for an address, use your current location, or click on the map to change it.
          </p>
        </div>
      )}

      {!position && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
          <div className="flex items-center space-x-2 text-sm">
            <MapPin className="h-4 w-4 text-amber-600" />
            <span className="text-amber-800">
              <strong>No location selected yet</strong>
            </span>
          </div>
          <p className="text-xs text-amber-600 mt-1">
            Please search for an address, use your current location, or click on the map above to set your delivery location.
          </p>
        </div>
      )}
    </div>
  );
};

export default DeliveryLocationMap;
