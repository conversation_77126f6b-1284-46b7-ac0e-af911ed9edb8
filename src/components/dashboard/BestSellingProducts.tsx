import { useState, useEffect } from 'react';
import { TrendingUp, Star, ShoppingCart, Package, Eye, ArrowUp, ArrowDown, Heart } from 'lucide-react';
import { getBestSellingProducts, BestSellingProduct } from '../../services/analyticsService';
import { realTimeService } from '../../services/realTimeService';
import { liveDataService } from '../../services/liveDataService';
import { getActiveCategories } from '../../services/liveCategoryService';
import ProductDetailsModal from '../products/ProductDetailsModal';
import { formatCurrency } from '../../utils/currency';

interface BestSellingProductsProps {
  userType: 'client' | 'reseller';
  customerId?: string;
  customerName?: string;
  onAddToCart?: (product: any) => void;
  onViewProduct?: (productId: string) => void;
  onAddToWishlist?: (product: any) => void;
}

const BestSellingProducts = ({ userType, customerId, customerName, onAddToCart, onViewProduct, onAddToWishlist }: BestSellingProductsProps) => {
  const [bestSellingProducts, setBestSellingProducts] = useState<BestSellingProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [showProductModal, setShowProductModal] = useState(false);

  useEffect(() => {
    loadProductData();

    // Subscribe to real-time updates
    const unsubscribeProduct = realTimeService.subscribe('product-updated', handleProductUpdate);
    const unsubscribeStock = realTimeService.subscribe('stock-updated', handleStockUpdate);
    const unsubscribePrice = realTimeService.subscribe('price-updated', handlePriceUpdate);
    const unsubscribeOrder = realTimeService.subscribe('order-updated', handleOrderUpdate);

    // Subscribe to Supabase real-time updates
    const productSubscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Real-time product update:', payload);
      loadProductData();
    });

    const orderSubscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Real-time order update:', payload);
      // Reload best-selling products when orders change
      loadBestSellingProducts();
    });

    return () => {
      unsubscribeProduct();
      unsubscribeStock();
      unsubscribePrice();
      unsubscribeOrder();
      productSubscription.unsubscribe();
      orderSubscription.unsubscribe();
    };
  }, []);

  const loadProductData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load only best-selling products
      const bestSellingData = await loadBestSellingProducts();
      setBestSellingProducts(bestSellingData);
    } catch (err) {
      setError('Failed to load product data');
      console.error('Error loading product data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadBestSellingProducts = async (): Promise<BestSellingProduct[]> => {
    try {
      const data = await getBestSellingProducts(4);
      return data;
    } catch (err) {
      console.error('Error loading best selling products:', err);
      return [];
    }
  };



  // Real-time event handlers
  const handleProductUpdate = (event: any) => {
    const { productId, newData } = event.data;
    setBestSellingProducts(prev => prev.map(product =>
      product.id === productId ? { ...product, ...newData } : product
    ));
  };

  const handleStockUpdate = (event: any) => {
    const { productId, newStock } = event.data;
    setBestSellingProducts(prev => prev.map(product =>
      product.id === productId ? { ...product, stock: newStock, inStock: newStock > 0 } : product
    ));
  };

  const handlePriceUpdate = (event: any) => {
    const { productId, newPrice, newResellerPrice } = event.data;
    setBestSellingProducts(prev => prev.map(product =>
      product.id === productId ? {
        ...product,
        price: newPrice || product.price,
        resellerPrice: newResellerPrice || product.resellerPrice
      } : product
    ));
  };

  const handleOrderUpdate = (event: any) => {
    // When orders change, reload best-selling products to reflect new sales data
    loadBestSellingProducts().then(data => setBestSellingProducts(data));
  };

  const handleAddToCart = (product: any) => {
    if (onAddToCart) {
      const cartProduct = {
        id: parseInt(product.id.replace('PRD-', '')) || parseInt(product.id),
        title: product.title,
        category: product.category,
        price: userType === 'reseller' ? product.resellerPrice : product.price,
        resellerPrice: product.resellerPrice,
        image: product.featuredImage || product.image,
        featuredImage: product.featuredImage || product.image,
        rating: product.rating,
        inStock: product.inStock,
        stock: product.stock
      };
      onAddToCart(cartProduct);
    }
  };

  const handleAddToWishlist = (product: any) => {
    if (onAddToWishlist) {
      onAddToWishlist(product);
    }
  };

  const handleViewProduct = (productId: string) => {
    setSelectedProductId(productId);
    setShowProductModal(true);

    // Also call the optional callback
    if (onViewProduct) {
      onViewProduct(productId);
    }
  };

  const handleCloseModal = () => {
    setShowProductModal(false);
    setSelectedProductId(null);
  };



  const formatPrice = (price: number) => {
    return formatCurrency(price);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32 mb-3"></div>
              <div className="bg-gray-200 rounded h-4 mb-2"></div>
              <div className="bg-gray-200 rounded h-3 w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadProductData}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-orange-100 to-orange-200 p-3 rounded-xl">
            <TrendingUp className="h-6 w-6 text-orange-600" />
          </div>
          <div>
            <h3 className="text-lg sm:text-xl font-bold text-gray-900">Best Selling Products</h3>
            <p className="text-xs sm:text-sm text-gray-600">Top 4 products from last month</p>
          </div>
        </div>
        {userType === 'reseller' && (
          <div className="bg-gradient-to-r from-teal-50 to-teal-100 px-3 py-1 rounded-full self-start sm:self-auto">
            <span className="text-xs sm:text-sm font-medium text-teal-700">Reseller Pricing</span>
          </div>
        )}
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {bestSellingProducts.map((product, index) => {
          const displayPrice = userType === 'reseller' ? product.resellerPrice : product.price;
          const savings = userType === 'reseller' ? product.price - product.resellerPrice : 0;
          
          return (
            <div
              key={product.id}
              className="group relative bg-gradient-to-br from-gray-50 to-white border border-gray-200 rounded-xl p-4 hover:shadow-lg hover:border-orange-200 transition-all duration-300 hover:-translate-y-1"
            >
              {/* Rank Badge */}
              <div className="absolute -top-2 -left-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center shadow-lg">
                {index + 1}
              </div>

              {/* Growth Indicator */}
              <div className="absolute top-2 right-2 flex items-center space-x-1">
                {product.growth > 0 ? (
                  <ArrowUp className="h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDown className="h-3 w-3 text-red-500" />
                )}
                <span className={`text-xs font-medium ${product.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(product.growth || 0).toFixed(1)}%
                </span>
              </div>

              {/* Product Image */}
              <div className="bg-gray-100 rounded-lg h-24 mb-3 flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                <Package className="h-8 w-8 text-gray-400" />
              </div>

              {/* Product Info */}
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-orange-600 transition-colors">
                  {product.title}
                </h4>
                
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 text-yellow-400 fill-current" />
                  <span className="text-xs text-gray-600">{product.rating}</span>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-gray-900">
                      {formatPrice(displayPrice)}
                    </span>
                    {userType === 'reseller' && savings > 0 && (
                      <span className="text-xs text-gray-500 line-through">
                        {formatPrice(product.price)}
                      </span>
                    )}
                  </div>
                  
                  {userType === 'reseller' && savings > 0 && (
                    <div className="text-xs text-green-600 font-medium">
                      Save {formatPrice(savings)}
                    </div>
                  )}
                </div>

                {/* Sales Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{formatNumber(product.unitsSold || 0)} sold</span>
                  <span className={`px-2 py-1 rounded-full ${product.inStock ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                    {product.inStock ? `${product.stock} left` : 'Out of stock'}
                  </span>
                </div>

                {/* Action Buttons - Three buttons in a row */}
                <div className="flex space-x-1 pt-2">
                  <button
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.inStock}
                    className={`flex-1 px-2 py-2 rounded-lg text-xs font-medium transition-all flex items-center justify-center space-x-1 ${
                      product.inStock
                        ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white hover:from-teal-600 hover:to-teal-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <ShoppingCart className="h-3 w-3" />
                    <span>Cart</span>
                  </button>

                  <button
                    onClick={() => handleViewProduct(product.id)}
                    className="px-2 py-2 bg-gray-100 text-gray-700 rounded-lg text-xs font-medium hover:bg-gray-200 transition-colors flex items-center justify-center"
                    title="View Details"
                    aria-label="View product details"
                  >
                    <Eye className="h-3 w-3" />
                  </button>

                  <button
                    onClick={() => handleAddToWishlist(product)}
                    className="px-2 py-2 bg-amber-100 text-amber-700 rounded-lg text-xs font-medium hover:bg-amber-200 transition-colors flex items-center justify-center"
                    title="Add to Wishlist"
                    aria-label="Add to wishlist"
                  >
                    <Heart className="h-3 w-3" />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer Stats */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 text-center">
          {activeTab === 'best-selling' ? (
            <>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {formatNumber(bestSellingProducts.reduce((sum, p) => sum + (p.unitsSold || 0), 0))}
                </p>
                <p className="text-xs text-gray-600">Total Units Sold</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {formatPrice(bestSellingProducts.reduce((sum, p) => sum + (p.revenue || 0), 0))}
                </p>
                <p className="text-xs text-gray-600">Total Revenue</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-green-600">
                  +{bestSellingProducts.length > 0 ? (bestSellingProducts.reduce((sum, p) => sum + (p.growth || 0), 0) / bestSellingProducts.length).toFixed(1) : '0.0'}%
                </p>
                <p className="text-xs text-gray-600">Avg Growth</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {bestSellingProducts.length > 0 ? (bestSellingProducts.reduce((sum, p) => sum + (p.rating || 0), 0) / bestSellingProducts.length).toFixed(1) : '0.0'}
                </p>
                <p className="text-xs text-gray-600">Avg Rating</p>
              </div>
            </>
          ) : (
            <>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {latestProducts.length}
                </p>
                <p className="text-xs text-gray-600">New Products</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {latestProducts.filter(p => p.inStock).length}
                </p>
                <p className="text-xs text-gray-600">In Stock</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {formatPrice(latestProducts.reduce((sum, p) => sum + (p.price || 0), 0) / Math.max(latestProducts.length, 1))}
                </p>
                <p className="text-xs text-gray-600">Avg Price</p>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-lg sm:text-2xl font-bold text-gray-900">
                  {latestProducts.length > 0 ? (latestProducts.reduce((sum, p) => sum + (p.rating || 0), 0) / latestProducts.length).toFixed(1) : '0.0'}
                </p>
                <p className="text-xs text-gray-600">Avg Rating</p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Product Details Modal */}
      <ProductDetailsModal
        productId={selectedProductId}
        isOpen={showProductModal}
        onClose={handleCloseModal}
        userType={userType}
        customerId={customerId}
        customerName={customerName}
        onAddToCart={onAddToCart}
      />
    </div>
  );
};

export default BestSellingProducts;
