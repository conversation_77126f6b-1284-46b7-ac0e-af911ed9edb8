import { useState, useEffect } from 'react';
import { X, Plus, Minus, Trash2, ShoppingBag, Tag, Percent, Gift, AlertCircle, CheckCircle } from 'lucide-react';
import { validatePromoCode } from '../../services/promoCodeService';
import { PromoCode } from '../../types/promoCode';
import { createOrder } from '../../services/orderService';
import CheckoutModal from '../checkout/CheckoutModal';
import { liveDataService } from '../../services/liveDataService';

interface CartItem {
  id: number;
  title: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
}

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
  items: CartItem[];
  onUpdateQuantity: (id: number, quantity: number) => void;
  onRemoveItem: (id: number) => void;
  onClearCart: () => void;
  userType?: string;
  user?: {
    id: string;
    fullName: string;
    email: string;
  };
}

const Cart = ({ isOpen, onClose, items, onUpdateQuantity, onRemoveItem, onClearCart, userType = 'client', user }: CartProps) => {
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null);
  const [promoError, setPromoError] = useState('');
  const [promoSuccess, setPromoSuccess] = useState('');
  const [isValidatingPromo, setIsValidatingPromo] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [availablePromoCodes, setAvailablePromoCodes] = useState<PromoCode[]>([]);
  const [showPromoSuggestions, setShowPromoSuggestions] = useState(false);

  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = subtotal > 200 ? 0 : 25;
  const discountAmount = appliedPromo ? calculateDiscount(subtotal, appliedPromo) : 0;
  const total = subtotal + deliveryFee - discountAmount;

  // Load available promo codes on mount
  useEffect(() => {
    loadAvailablePromoCodes();
  }, []);

  const loadAvailablePromoCodes = async () => {
    try {
      // This would typically fetch from your promo code service
      // For now, we'll use mock data
      const mockPromoCodes: PromoCode[] = [
        {
          id: 'PROMO-001',
          code: 'SAVE10',
          description: '10% off orders over 100 Dh',
          type: 'percentage',
          value: 10,
          minOrderAmount: 100,
          usageLimit: 1000,
          usedCount: 250,
          isActive: true,
          validFrom: '2024-01-01T00:00:00Z',
          validUntil: '2024-12-31T23:59:59Z',
          createdBy: 'admin',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 'PROMO-002',
          code: 'FREESHIP',
          description: 'Free shipping on orders over 200 Dh',
          type: 'fixed',
          value: 25,
          minOrderAmount: 200,
          usageLimit: 500,
          usedCount: 150,
          isActive: true,
          validFrom: '2024-01-01T00:00:00Z',
          validUntil: '2024-12-31T23:59:59Z',
          createdBy: 'admin',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ];
      setAvailablePromoCodes(mockPromoCodes);
    } catch (error) {
      console.error('Error loading promo codes:', error);
    }
  };

  function calculateDiscount(amount: number, promo: PromoCode): number {
    if (promo.type === 'percentage') {
      const discount = (amount * promo.value) / 100;
      return promo.maxDiscount ? Math.min(discount, promo.maxDiscount) : discount;
    } else {
      return promo.value;
    }
  }

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) return;

    setIsValidatingPromo(true);
    setPromoError('');
    setPromoSuccess('');

    try {
      const validation = await validatePromoCode(promoCode, subtotal);
      if (validation.isValid) {
        setAppliedPromo(validation.promoCode!);
        setPromoSuccess(`Promo code applied! You saved ${calculateDiscount(subtotal, validation.promoCode!).toFixed(2)} Dh`);
        setPromoCode('');
        setShowPromoSuggestions(false);
      } else {
        setPromoError(validation.error || 'Invalid promo code');
        setAppliedPromo(null);
      }
    } catch (error) {
      setPromoError('Error validating promo code');
      setAppliedPromo(null);
    } finally {
      setIsValidatingPromo(false);
    }
  };

  const handleRemovePromo = () => {
    setAppliedPromo(null);
    setPromoCode('');
    setPromoError('');
    setPromoSuccess('');
  };

  const handleApplySuggestedPromo = async (suggestedPromo: PromoCode) => {
    setPromoCode(suggestedPromo.code);
    setShowPromoSuggestions(false);

    // Auto-apply the suggested promo
    setIsValidatingPromo(true);
    setPromoError('');
    setPromoSuccess('');

    try {
      const validation = await validatePromoCode(suggestedPromo.code, subtotal);
      if (validation.isValid) {
        setAppliedPromo(validation.promoCode!);
        setPromoSuccess(`Promo code applied! You saved ${calculateDiscount(subtotal, validation.promoCode!).toFixed(2)} Dh`);
        setPromoCode('');
      } else {
        setPromoError(validation.error || 'Invalid promo code');
        setAppliedPromo(null);
      }
    } catch (error) {
      setPromoError('Error validating promo code');
      setAppliedPromo(null);
    } finally {
      setIsValidatingPromo(false);
    }
  };

  const getSuggestedPromoCodes = () => {
    return availablePromoCodes.filter(promo =>
      promo.isActive &&
      (!promo.minOrderAmount || subtotal >= promo.minOrderAmount) &&
      promo.id !== appliedPromo?.id
    );
  };

  const handleCheckout = async (orderData: any) => {
    try {
      console.log('Processing order:', orderData);
      
      // Create the order with proper structure
      const orderPayload = {
        customerId: user?.id || 'anonymous-user',
        customerName: user?.fullName || 'Anonymous User',
        customerEmail: user?.email || '<EMAIL>',
        items: items.map(item => ({
          id: typeof item.id === 'string' ? item.id : item.id.toString(),
          title: item.title,
          category: item.category,
          price: item.price,
          quantity: item.quantity,
          image: item.image
        })),
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        discount: discountAmount,
        total: total,
        status: 'pending' as const,
        paymentMethod: orderData.paymentMethod === 'cash' ? 'cash' : 'card',
        paymentStatus: 'pending' as const,
        deliveryAddress: {
          address: orderData.address,
          city: orderData.city,
          postalCode: orderData.postalCode,
          coordinates: orderData.deliveryCoordinates || null
        },
        selectedBranch: null, // Will be set by the system
        promoCode: appliedPromo?.code,
        notes: orderData.notes
      };

      const newOrder = await createOrder(orderPayload);
      console.log('Order created successfully:', newOrder);

      if (!newOrder) {
        throw new Error('Order creation failed - no order returned');
      }

      // Clear cart and reset promo
      onClearCart();
      setAppliedPromo(null);
      setPromoCode('');

      // Show success message (you can add a toast here)
      alert(`Order placed successfully! Order ID: ${newOrder.id}`);

    } catch (error) {
      console.error('Error creating order:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error placing order. Please try again.';
      alert(errorMessage);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50">
        <div className="bg-white w-full sm:w-[500px] sm:max-w-lg h-full sm:h-auto sm:max-h-[90vh] sm:rounded-2xl shadow-2xl flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <ShoppingBag className="h-6 w-6 text-teal-600" />
              <h2 className="text-xl font-bold text-gray-900">Shopping Cart</h2>
              <span className="bg-teal-100 text-teal-800 text-sm font-medium px-2 py-1 rounded-full">
                {items.length}
              </span>
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">Your cart is empty</p>
                <p className="text-gray-400">Add some products to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map(item => (
                  <div key={item.id} className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
                    <img src={item.image} alt={item.title} className="w-16 h-16 object-cover rounded-lg" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 text-sm">{item.title}</h3>
                      <p className="text-gray-500 text-xs">{item.category}</p>
                      <p className="text-teal-600 font-bold">{item.price.toFixed(2)} DH</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        {item.quantity === 1 ? <Trash2 className="h-4 w-4" /> : <Minus className="h-4 w-4" />}
                      </button>
                      <span className="w-8 text-center font-medium">{item.quantity}</span>
                      <button
                        onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}

                {/* Enhanced Promo Code Section */}
                {items.length > 0 && (
                  <div className="border-t pt-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium text-gray-900 flex items-center">
                        <Tag className="h-4 w-4 mr-2 text-teal-600" />
                        Promo Code
                      </h3>
                      {!appliedPromo && getSuggestedPromoCodes().length > 0 && (
                        <button
                          onClick={() => setShowPromoSuggestions(!showPromoSuggestions)}
                          className="text-xs text-teal-600 hover:text-teal-700 font-medium flex items-center"
                        >
                          <Gift className="h-3 w-3 mr-1" />
                          Available offers
                        </button>
                      )}
                    </div>

                    {appliedPromo ? (
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex items-start space-x-2">
                            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                            <div>
                              <p className="text-green-800 font-semibold text-sm">{appliedPromo.code}</p>
                              <p className="text-green-600 text-xs">{appliedPromo.description}</p>
                              <p className="text-green-700 text-xs font-medium mt-1">
                                You saved {discountAmount.toFixed(2)} Dh
                              </p>
                            </div>
                          </div>
                          <button
                            onClick={handleRemovePromo}
                            className="text-red-600 hover:text-red-800 text-xs font-medium px-2 py-1 rounded hover:bg-red-50 transition-colors"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex space-x-2">
                          <div className="flex-1 relative">
                            <input
                              type="text"
                              placeholder="Enter promo code"
                              value={promoCode}
                              onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 pr-8"
                              onKeyPress={(e) => e.key === 'Enter' && handleApplyPromo()}
                            />
                            <Percent className="absolute right-2 top-2.5 h-4 w-4 text-gray-400" />
                          </div>
                          <button
                            onClick={handleApplyPromo}
                            disabled={isValidatingPromo || !promoCode.trim()}
                            className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 text-sm rounded-lg hover:from-teal-700 hover:to-teal-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all font-medium"
                          >
                            {isValidatingPromo ? (
                              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                            ) : (
                              'Apply'
                            )}
                          </button>
                        </div>

                        {/* Suggested Promo Codes */}
                        {showPromoSuggestions && getSuggestedPromoCodes().length > 0 && (
                          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-3">
                            <h4 className="text-xs font-medium text-amber-800 mb-2 flex items-center">
                              <Gift className="h-3 w-3 mr-1" />
                              Available for your order:
                            </h4>
                            <div className="space-y-2">
                              {getSuggestedPromoCodes().slice(0, 2).map(promo => (
                                <div key={promo.id} className="flex items-center justify-between bg-white rounded p-2">
                                  <div>
                                    <p className="text-xs font-medium text-gray-900">{promo.code}</p>
                                    <p className="text-xs text-gray-600">{promo.description}</p>
                                  </div>
                                  <button
                                    onClick={() => handleApplySuggestedPromo(promo)}
                                    className="text-xs bg-teal-600 text-white px-2 py-1 rounded hover:bg-teal-700 transition-colors"
                                  >
                                    Apply
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Success/Error Messages */}
                    {promoSuccess && (
                      <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-2 rounded-lg">
                        <CheckCircle className="h-4 w-4" />
                        <p className="text-xs font-medium">{promoSuccess}</p>
                      </div>
                    )}
                    {promoError && (
                      <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-2 rounded-lg">
                        <AlertCircle className="h-4 w-4" />
                        <p className="text-xs font-medium">{promoError}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer with totals and checkout */}
          {items.length > 0 && (
            <div className="border-t p-6 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{subtotal.toFixed(2)} DH</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery</span>
                  <span>{deliveryFee === 0 ? 'Free' : `${deliveryFee.toFixed(2)} DH`}</span>
                </div>
                {discountAmount > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-{discountAmount.toFixed(2)} DH</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-bold">
                  <span>Total</span>
                  <span>{total.toFixed(2)} DH</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={() => setShowCheckout(true)}
                  className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Proceed to Checkout
                </button>
                <button
                  onClick={onClearCart}
                  className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors text-sm"
                >
                  Clear Cart
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <CheckoutModal
        isOpen={showCheckout}
        onClose={() => setShowCheckout(false)}
        cartItems={items}
        subtotal={subtotal}
        deliveryFee={deliveryFee}
        discountAmount={discountAmount}
        appliedPromo={appliedPromo}
        onCheckout={handleCheckout}
      />
    </>
  );
};

export default Cart;
